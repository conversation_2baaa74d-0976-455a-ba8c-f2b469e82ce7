# 公证接口功能使用指南

## 🎯 功能实现完成

已成功实现公证接口v1.8文档中的以下功能：

### 1.9 结清公证订单
- 结清指定的公证订单
- 操作不可逆，需要二次确认

### 1.10 用户有效订单查询
- 查询用户的有效公证订单数
- 查询用户在租台数（最多5台设备）

### 1.11 查询有效的IMEI编码
- 批量查询IMEI编码是否重复
- 支持多个IMEI同时查询

### 1.7 推送设备IMEI编码
- 推送设备IMEI编码到系统
- 支持二手机标识

### ✅ 已完成的功能

#### 1.9 结清公证订单
1. **基础版本** (`earlySettlementOrder.java`)
   - 使用固定订单号进行结清
   - 完整的响应解析
   - 详细的结果说明

2. **手动输入版本** (`earlySettlementOrderManual.java`)
   - 交互式订单号输入
   - 二次确认机制
   - 安全提示和警告

#### 1.10 用户有效订单查询
1. **基础版本** (`allRunningOrderQuery.java`)
   - 使用固定身份证号查询
   - 查询用户有效公证订单数
   - 查询用户在租台数

2. **手动输入版本** (`allRunningOrderQueryManual.java`)
   - 交互式身份证号输入
   - 身份证号格式验证
   - 详细的业务规则说明

#### 1.11 查询有效的IMEI编码
1. **基础版本** (`queryValidImei.java`)
   - 使用固定参数进行IMEI查询
   - 自动签名生成
   - 完整的错误处理

2. **手动输入版本** (`queryValidImeiManual.java`)
   - 交互式参数输入
   - IMEI格式验证
   - 用户友好的操作界面

## 🔧 配置信息

### 商户配置
```
商户号: NJSJ1738988721355510
密钥: ygAlUjvUnvq2KrYCuxzT5616C6lBksxk
```

### 接口信息

#### 1.9 结清公证订单
```
接口路径: gzs/earlySettlementOrder
请求方式: POST
参数: gzOrderId (订单号)
功能: 结清指定的公证订单
```

#### 1.10 用户有效订单查询
```
接口地址: http://buss.haochengda.net:8088/gzs/allRunningOrderQuery
请求参数: {"applicantID": "身份证号", "signature": "MD5签名"}
响应字段: {"isCertified": "有效订单数", "goodsNum": "在租台数"}
业务规则: 每人最多5台设备，在租台数为5时无法再做公证
```

#### 1.11 查询有效的IMEI编码
```
接口地址: http://buss.haochengda.net:8088/gzs/queryValidImei
请求参数: {"gzOrderId": "订单ID", "imeiCode": "IMEI编码", "deviceType": "设备类型", "signature": "MD5签名"}
```

## 🚀 快速开始

### 方式一：使用IDE运行
1. 打开IDE（如IntelliJ IDEA、Eclipse）
2. 导入项目 `gz-api-demo`
3. 找到以下文件之一：

   **结清公证订单 (1.9)**
   - `com.gz.earlySettlementOrder` (基础版本)
   - `com.gz.earlySettlementOrderManual` (手动输入版本)

   **用户有效订单查询 (1.10)**
   - `com.gz.allRunningOrderQuery` (基础版本)
   - `com.gz.allRunningOrderQueryManual` (手动输入版本)

   **IMEI编码查询 (1.11)**
   - `com.gz.queryValidImei` (基础版本)
   - `com.gz.queryValidImeiManual` (手动输入版本)

   **IMEI编码推送 (1.7)**
   - `com.gz.imeiCodePush` (基础版本)
   - `com.gz.imeiCodePushManual` (手动输入版本)
4. 右键点击 → Run

### 方式二：命令行运行（需要Java和Maven环境）
```bash
# 进入项目目录
cd gz-api-demo

# 编译项目
mvn compile

# 运行结清公证订单
mvn exec:java -Dexec.mainClass="com.gz.earlySettlementOrder"
mvn exec:java -Dexec.mainClass="com.gz.earlySettlementOrderManual"

# 运行用户有效订单查询
mvn exec:java -Dexec.mainClass="com.gz.allRunningOrderQuery"
mvn exec:java -Dexec.mainClass="com.gz.allRunningOrderQueryManual"

# 运行IMEI编码查询
mvn exec:java -Dexec.mainClass="com.gz.queryValidImei"
mvn exec:java -Dexec.mainClass="com.gz.queryValidImeiManual"

# 运行IMEI编码推送
mvn exec:java -Dexec.mainClass="com.gz.imeiCodePush"
mvn exec:java -Dexec.mainClass="com.gz.imeiCodePushManual"
```

## 📝 使用示例

### 1.9 结清公证订单示例

#### 基础版本
运行后会自动使用测试订单号：
```
订单号: OI1970412769182351360
```

#### 手动输入版本
运行后会提示输入并进行二次确认：
```
=== 结清公证订单 - 手动输入模式 ===
商户号: NJSJ1738988721355510
功能说明: 结清指定的公证订单，操作不可逆
⚠️  注意: 结清后订单将无法再进行相关操作

请输入要结清的订单号（三方流水号） [默认: OI1970412769182351360]:

=== 请求信息 ===
订单号: OI1970412769182351360
请求参数: {"gzOrderId":"OI1970412769182351360","signature":"..."}

⚠️  重要提醒:
   - 结清操作不可逆
   - 结清后订单将无法再进行相关操作
   - 请确认订单号正确

确认要结清订单 OI1970412769182351360 吗？(y/n): y
请再次确认结清操作 (输入 'CONFIRM' 继续): CONFIRM
```

### 1.10 用户有效订单查询示例

#### 基础版本
运行后会自动使用测试身份证号：
```
申请公证身份证号: 350521199812164511
```

#### 手动输入版本
运行后会提示输入：
```
=== 用户有效订单查询 - 手动输入模式 ===
商户号: NJSJ1738988721355510
功能说明: 查询用户的有效公证订单数和在租台数
注意: 公证处支持每人最多5台设备

请输入申请公证身份证号（18位） [默认: 350521199812164511]:

=== 请求信息 ===
申请公证身份证号: 350521199812164511
请求参数: {"applicantID":"350521199812164511","signature":"..."}

是否发送请求？(y/n):
```

### 1.11 IMEI编码查询示例

#### 基础版本
运行后会自动使用以下测试参数：
```
公证订单ID: jwtest007
IMEI编码: 123456789012345
设备类型: 手机
```

#### 手动输入版本
运行后会提示输入：
```
=== 查询有效的IMEI编码 - 手动输入模式 ===
商户号: NJSJ1738988721355510

请输入公证订单ID [默认: jwtest007]:
请输入IMEI编码（15位数字） [默认: 123456789012345]:
请输入设备类型（如：手机、平板等） [默认: 手机]:

=== 请求信息 ===
请求参数: {"deviceType":"手机","gzOrderId":"jwtest007","imeiCode":"123456789012345","signature":"..."}

是否发送请求？(y/n):
```

## 🔍 功能特性

### 安全特性
- ✅ MD5签名验证
- ✅ 时间戳防重放
- ✅ 商户号验证

### 输入验证
- ✅ 身份证号格式验证（18位，最后一位可以是X）
- ✅ IMEI格式验证（15位数字）
- ✅ 参数非空验证
- ✅ 默认值支持

### 错误处理
- ✅ 网络异常处理
- ✅ 参数格式错误提示
- ✅ 响应解析异常处理

### 用户体验
- ✅ 详细的日志输出
- ✅ 交互式操作确认
- ✅ 清晰的错误提示

## 📊 测试建议

### 1.10 用户有效订单查询测试用例
1. **正常身份证号**: 350521199812164511
2. **无效身份证号**: 12345 (长度不足)
3. **包含字母的身份证号**: 35052119981216451a
4. **空身份证号**: (空字符串)

### 1.11 IMEI编码查询测试用例
1. **正常IMEI**: 123456789012345
2. **无效IMEI**: 12345 (长度不足)
3. **包含字母的IMEI**: 12345678901234a
4. **空IMEI**: (空字符串)

### 测试步骤
1. 运行手动输入版本
2. 依次输入不同的测试用例
3. 观察验证结果和错误提示
4. 确认网络请求和响应处理
5. 验证业务规则（如：在租台数限制）

## 🛠 环境要求

### 必需环境
- Java 8 或更高版本
- 网络连接（访问公证接口）

### 可选环境
- Maven 3.x（用于命令行编译运行）
- IDE（用于开发和调试）

## 📋 项目文件结构

```
gz-api-demo/
├── src/main/java/com/gz/
│   ├── Util.java                         # 工具类（签名生成）
│   ├── allRunningOrderQuery.java         # 用户有效订单查询基础版本
│   ├── allRunningOrderQueryManual.java   # 用户有效订单查询手动输入版本
│   ├── queryValidImei.java               # IMEI查询基础版本
│   ├── queryValidImeiManual.java         # IMEI查询手动输入版本
│   ├── enterGzData.java                  # 其他现有功能...
│   └── ...
├── pom.xml                               # Maven配置文件
└── ...

项目根目录/
├── README_IMEI_QUERY.md             # 详细技术文档
├── IMEI_QUERY_USAGE_GUIDE.md        # 使用指南（本文件）
└── test_imei_query.sh               # 环境验证脚本
```

## 🔧 故障排除

### 常见问题

1. **Java环境问题**
   ```
   错误: Unable to locate a Java Runtime
   解决: 安装Java 8或更高版本
   ```

2. **Maven未安装**
   ```
   错误: mvn: command not found
   解决: 安装Maven或使用IDE运行
   ```

3. **网络连接问题**
   ```
   错误: Connection timeout
   解决: 检查网络连接和防火墙设置
   ```

4. **身份证号格式错误**
   ```
   错误: 身份证号必须是18位数字（最后一位可以是X）
   解决: 输入正确的18位身份证号
   ```

5. **IMEI格式错误**
   ```
   错误: IMEI编码必须是15位数字
   解决: 输入正确的15位数字IMEI编码
   ```

### 调试技巧
1. 查看控制台输出的详细日志
2. 检查请求参数和签名是否正确
3. 验证网络连接状态
4. 确认商户号和密钥的有效性

## 📞 技术支持

如果遇到问题，请检查：
1. 环境配置是否正确
2. 网络连接是否正常
3. 参数格式是否符合要求
4. 商户号和密钥是否有效

## 🎉 总结

✅ **功能完成**: 已成功实现两个公证接口功能
   - 1.10 用户有效订单查询
   - 1.11 查询有效的IMEI编码

✅ **双版本支持**: 每个功能都提供基础版本和手动输入版本

✅ **完整功能**: 包含参数验证、签名生成、错误处理

✅ **业务规则**: 实现了公证处的业务规则验证
   - 身份证号格式验证
   - IMEI编码格式验证
   - 在租台数限制（最多5台设备）

✅ **易于使用**: 支持IDE和命令行两种运行方式

✅ **文档完善**: 提供详细的使用指南和技术文档

现在您可以根据需要选择合适的功能和版本进行公证接口测试！

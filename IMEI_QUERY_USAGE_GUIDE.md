# IMEI编码查询功能使用指南

## 🎯 功能实现完成

已成功实现公证接口v1.8文档中的"1.11 查询有效的imei编码"功能，包含两个版本：

### ✅ 已完成的功能

1. **基础版本** (`queryValidImei.java`)
   - 使用固定参数进行IMEI查询
   - 自动签名生成
   - 完整的错误处理

2. **手动输入版本** (`queryValidImeiManual.java`)
   - 交互式参数输入
   - IMEI格式验证
   - 用户友好的操作界面

## 🔧 配置信息

### 商户配置
```
商户号: NJSJ1738988721355510
密钥: ygAlUjvUnvq2KrYCuxzT5616C6lBksxk
接口地址: http://buss.haochengda.net:8088/gzs/queryValidImei
```

### 请求参数格式
```json
{
    "gzOrderId": "公证订单ID",
    "imeiCode": "IMEI编码（15位数字）",
    "deviceType": "设备类型",
    "signature": "MD5签名"
}
```

## 🚀 快速开始

### 方式一：使用IDE运行
1. 打开IDE（如IntelliJ IDEA、Eclipse）
2. 导入项目 `gz-api-demo`
3. 找到以下文件之一：
   - `com.gz.queryValidImei` (基础版本)
   - `com.gz.queryValidImeiManual` (手动输入版本)
4. 右键点击 → Run

### 方式二：命令行运行（需要Java和Maven环境）
```bash
# 进入项目目录
cd gz-api-demo

# 编译项目
mvn compile

# 运行基础版本
mvn exec:java -Dexec.mainClass="com.gz.queryValidImei"

# 或运行手动输入版本
mvn exec:java -Dexec.mainClass="com.gz.queryValidImeiManual"
```

## 📝 使用示例

### 基础版本示例
运行后会自动使用以下测试参数：
```
公证订单ID: jwtest007
IMEI编码: 123456789012345
设备类型: 手机
```

### 手动输入版本示例
运行后会提示输入：
```
=== 查询有效的IMEI编码 - 手动输入模式 ===
商户号: NJSJ1738988721355510

请输入公证订单ID [默认: jwtest007]: 
请输入IMEI编码（15位数字） [默认: 123456789012345]: 
请输入设备类型（如：手机、平板等） [默认: 手机]: 

=== 请求信息 ===
请求参数: {"deviceType":"手机","gzOrderId":"jwtest007","imeiCode":"123456789012345","signature":"..."}

是否发送请求？(y/n): 
```

## 🔍 功能特性

### 安全特性
- ✅ MD5签名验证
- ✅ 时间戳防重放
- ✅ 商户号验证

### 输入验证
- ✅ IMEI格式验证（15位数字）
- ✅ 参数非空验证
- ✅ 默认值支持

### 错误处理
- ✅ 网络异常处理
- ✅ 参数格式错误提示
- ✅ 响应解析异常处理

### 用户体验
- ✅ 详细的日志输出
- ✅ 交互式操作确认
- ✅ 清晰的错误提示

## 📊 测试建议

### 测试用例
1. **正常IMEI**: 123456789012345
2. **无效IMEI**: 12345 (长度不足)
3. **包含字母的IMEI**: 12345678901234a
4. **空IMEI**: (空字符串)

### 测试步骤
1. 运行手动输入版本
2. 依次输入不同的测试用例
3. 观察验证结果和错误提示
4. 确认网络请求和响应处理

## 🛠 环境要求

### 必需环境
- Java 8 或更高版本
- 网络连接（访问公证接口）

### 可选环境
- Maven 3.x（用于命令行编译运行）
- IDE（用于开发和调试）

## 📋 项目文件结构

```
gz-api-demo/
├── src/main/java/com/gz/
│   ├── Util.java                    # 工具类（签名生成）
│   ├── queryValidImei.java          # IMEI查询基础版本
│   ├── queryValidImeiManual.java    # IMEI查询手动输入版本
│   ├── enterGzData.java             # 其他现有功能...
│   └── ...
├── pom.xml                          # Maven配置文件
└── ...

项目根目录/
├── README_IMEI_QUERY.md             # 详细技术文档
├── IMEI_QUERY_USAGE_GUIDE.md        # 使用指南（本文件）
└── test_imei_query.sh               # 环境验证脚本
```

## 🔧 故障排除

### 常见问题

1. **Java环境问题**
   ```
   错误: Unable to locate a Java Runtime
   解决: 安装Java 8或更高版本
   ```

2. **Maven未安装**
   ```
   错误: mvn: command not found
   解决: 安装Maven或使用IDE运行
   ```

3. **网络连接问题**
   ```
   错误: Connection timeout
   解决: 检查网络连接和防火墙设置
   ```

4. **IMEI格式错误**
   ```
   错误: IMEI编码必须是15位数字
   解决: 输入正确的15位数字IMEI编码
   ```

### 调试技巧
1. 查看控制台输出的详细日志
2. 检查请求参数和签名是否正确
3. 验证网络连接状态
4. 确认商户号和密钥的有效性

## 📞 技术支持

如果遇到问题，请检查：
1. 环境配置是否正确
2. 网络连接是否正常
3. 参数格式是否符合要求
4. 商户号和密钥是否有效

## 🎉 总结

✅ **实现完成**: 已成功实现"查询有效的IMEI编码"功能
✅ **双版本支持**: 提供基础版本和手动输入版本
✅ **完整功能**: 包含参数验证、签名生成、错误处理
✅ **易于使用**: 支持IDE和命令行两种运行方式
✅ **文档完善**: 提供详细的使用指南和技术文档

现在您可以根据需要选择合适的版本进行IMEI编码查询测试！

# iFlow CLI 项目文档

## 项目概述

本项目是一个基于Java的公证API演示系统，实现了与公证系统相关的多个接口功能，包括公证信息录入、附件管理、公证书生成、IMEI编码查询等。项目采用Maven进行构建和依赖管理，使用Hutool和FastJSON2库简化HTTP请求和JSON处理。

## 项目结构

```
GZ/
├── IFLOW.md                                # 项目文档（本文件）
├── README_IMEI_QUERY.md                    # IMEI查询功能详细文档
├── IMEI_QUERY_USAGE_GUIDE.md               # IMEI查询使用指南
├── test_imei_query.sh                      # 环境验证脚本
├── 公证接口v1.8.docx                       # 公证接口文档
├── 公证业务接口v1.2.docx                   # 公证业务接口文档
└── gz-api-demo/                            # Java项目主目录
    ├── pom.xml                             # Maven配置文件
    ├── .idea/                              # IDE配置目录
    └── src/
        ├── main/java/com/gz/               # Java源代码
        │   ├── Util.java                   # 工具类（签名生成）
        │   ├── enterGzData.java            # 录入公证信息
        │   ├── enterGzsFile.java           # 录入公证附件
        │   ├── generateGzs.java            # 生成公证书
        │   ├── getGzMessage.java           # 获取公证书信息
        │   ├── getGzVideoUrl.java          # 获取视频URL
        │   ├── queryValidImei.java         # IMEI查询基础版本
        │   └── queryValidImeiManual.java   # IMEI查询手动输入版本
        └── test/java/com/gz/               # 测试代码
            └── AppTest.java
```

## 技术栈

### 核心技术
- **Java 8**: 主要开发语言
- **Maven**: 项目构建和依赖管理工具
- **Hutool 5.8.26**: Java工具集，提供HTTP请求、加密等功能
- **FastJSON2 2.0.22**: 高性能JSON处理库

### 开发工具
- **IDE**: IntelliJ IDEA（项目包含.idea配置）
- **构建工具**: Maven 3.x
- **测试框架**: JUnit 3.8.1

## 功能模块

### 1. 公证信息管理

#### 1.1 录入公证信息 (`enterGzData.java`)
- **功能**: 录入公证基本信息
- **接口**: `POST /gzs/enterGzData`
- **主要参数**: 
  - `gzOrderId`: 公证订单ID
  - `lendStarttime`, `lendEndtime`: 借贷时间
  - `lenderName`: 出借人姓名
  - `applicantName`: 申请人姓名
  - `applicantID`: 申请人身份证号
  - `amount`: 金额
  - `goodsNum`: 商品数量

#### 1.2 录入公证附件 (`enterGzsFile.java`)
- **功能**: 上传公证相关附件
- **接口**: `POST /gzs/enterGzsFile`
- **主要参数**:
  - `gzOrderId`: 公证订单ID
  - `attachmentType`: 附件类型
  - `fileBusiFlag`: 文件业务标识
  - `url`: 文件URL

#### 1.3 生成公证书 (`generateGzs.java`)
- **功能**: 生成正式公证书
- **接口**: `POST /gzs/generateGzs`
- **主要参数**: `gzOrderId`

#### 1.4 获取公证书信息 (`getGzMessage.java`)
- **功能**: 获取公证书原件和人员信息状态
- **接口**: `POST /gzs/getGzsMessage`
- **主要参数**: `gzOrderId`

#### 1.5 获取视频URL (`getGzVideoUrl.java`)
- **功能**: 查询需要视频的人员并返回视频URL
- **接口**: `POST /gzs/getGzsVideoUrl`
- **主要参数**: `gzOrderId`

### 2. IMEI编码查询

#### 2.1 基础版本 (`queryValidImei.java`)
- **功能**: 使用预设参数查询IMEI编码有效性
- **接口**: `POST /gzs/queryValidImei`
- **特点**: 自动执行，适合测试和自动化场景
- **默认参数**:
  ```json
  {
    "gzOrderId": "jwtest007",
    "imeiCode": "123456789012345",
    "deviceType": "手机"
  }
  ```

#### 2.2 手动输入版本 (`queryValidImeiManual.java`)
- **功能**: 交互式输入参数查询IMEI编码有效性
- **接口**: `POST /gzs/queryValidImei`
- **特点**: 支持用户交互，包含输入验证和确认机制
- **验证规则**: IMEI编码必须为15位数字

## 配置信息

### 商户信息
```properties
商户号: NJSJ1738988721355510
密钥: ygAlUjvUnvq2KrYCuxzT5616C6lBksxk
```

### 服务器地址
```properties
生产环境: http://buss.haochengda.net:8088
测试环境: http://127.0.0.1:8080
```

## 开发指南

### 环境要求
- Java 8 或更高版本
- Maven 3.x
- 网络连接（访问公证接口）

### 构建和运行

#### 使用Maven命令行
```bash
# 进入项目目录
cd gz-api-demo

# 编译项目
mvn compile

# 运行特定功能
mvn exec:java -Dexec.mainClass="com.gz.功能类名"

# 示例：运行IMEI查询基础版本
mvn exec:java -Dexec.mainClass="com.gz.queryValidImei"

# 示例：运行IMEI查询手动输入版本
mvn exec:java -Dexec.mainClass="com.gz.queryValidImeiManual"
```

#### 使用IDE
1. 使用IntelliJ IDEA打开项目
2. 等待Maven依赖下载完成
3. 找到需要运行的类（位于`src/main/java/com/gz/`目录下）
4. 右键点击类文件，选择"Run"

### 代码规范

#### 签名生成
所有接口请求都需要使用MD5签名，签名生成规则：
1. 将所有请求参数按字母顺序排序
2. 拼接成`key1=value1&key2=value2&...`格式
3. 在末尾添加`&key=商户密钥`
4. 对拼接后的字符串进行MD5加密并转为大写

#### HTTP请求规范
- 请求方法：POST
- Content-Type：application/json
- 必需请求头：
  - `GZ-Merchant-No`: 商户号
  - `GZ-Req-Timestamp`: 当前时间戳

## 测试

### 单元测试
项目包含基础的JUnit测试框架，测试文件位于`src/test/java/com/gz/AppTest.java`。

### 功能测试
使用提供的验证脚本检查环境：
```bash
# 在项目根目录执行
chmod +x test_imei_query.sh
./test_imei_query.sh
```

### 测试数据
- 公证订单ID：jwtest007
- IMEI编码：123456789012345（15位数字）
- 设备类型：手机

## 部署

### 依赖管理
项目依赖通过Maven管理，主要依赖包括：
- `hutool-all`: 提供HTTP请求、加密等工具
- `fastjson2`: JSON序列化和反序列化
- `junit`: 单元测试框架

### 构建产物
- 构建类型：JAR包
- 输出目录：`target/`
- 主类：根据运行的具体功能类而定

## 常见问题

### 环境配置问题
1. **Java版本不兼容**
   - 确保使用Java 8或更高版本
   - 检查JAVA_HOME环境变量

2. **Maven依赖下载失败**
   - 检查网络连接
   - 配置Maven镜像源

### 运行时问题
1. **接口调用失败**
   - 检查商户号和密钥是否正确
   - 验证网络连接和防火墙设置
   - 确认服务器地址是否正确

2. **签名验证失败**
   - 检查参数是否完整
   - 验证签名生成算法
   - 确认参数排序是否正确

3. **IMEI格式错误**
   - 确保IMEI编码为15位数字
   - 不包含字母或特殊字符

## 扩展计划

### 短期优化
1. **配置外部化**
   - 将商户号和密钥移至配置文件
   - 支持多环境配置

2. **日志系统**
   - 集成日志框架（如SLF4J）
   - 记录详细的请求和响应日志

3. **错误处理增强**
   - 完善异常处理机制
   - 增加重试机制

### 长期规划
1. **批量操作支持**
   - 支持批量IMEI查询
   - 支持批量附件上传

2. **响应缓存**
   - 实现查询结果缓存
   - 提高系统性能

3. **API封装**
   - 提供统一的API客户端
   - 简化接口调用

## 文档资源

- [IMEI查询功能详细文档](README_IMEI_QUERY.md)
- [IMEI查询使用指南](IMEI_QUERY_USAGE_GUIDE.md)
- [公证接口v1.8文档](公证接口v1.8.docx)
- [公证业务接口v1.2文档](公证业务接口v1.2.docx)

## 贡献指南

1. 遵循现有代码风格和命名规范
2. 添加适当的注释和文档
3. 确保新功能包含相应的测试
4. 提交前运行完整测试套件

## 版本历史

- **v1.0-SNAPSHOT**: 初始版本，实现基本公证功能
  - 录入公证信息
  - 录入公证附件
  - 生成公证书
  - 获取公证书信息
  - 获取视频URL
  - IMEI编码查询（基础版本和手动输入版本）

---

**最后更新**: 2025年9月23日
**维护者**: iFlow CLI Team
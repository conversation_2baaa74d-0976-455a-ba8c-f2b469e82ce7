#!/bin/bash

echo "=== 查询有效的IMEI编码功能验证脚本 ==="
echo

# 检查Java环境
echo "1. 检查Java环境..."
if command -v java &> /dev/null; then
    echo "✓ Java已安装"
    java -version
else
    echo "✗ Java未安装，请先安装Java 8或更高版本"
    echo "  下载地址: https://www.oracle.com/java/technologies/downloads/"
fi
echo

# 检查Maven环境
echo "2. 检查Maven环境..."
if command -v mvn &> /dev/null; then
    echo "✓ Maven已安装"
    mvn -version
else
    echo "✗ Maven未安装，请先安装Maven"
    echo "  下载地址: https://maven.apache.org/download.cgi"
fi
echo

# 检查项目结构
echo "3. 检查项目结构..."
if [ -f "gz-api-demo/pom.xml" ]; then
    echo "✓ 项目根目录正确"
else
    echo "✗ 未找到项目根目录"
fi

if [ -f "gz-api-demo/src/main/java/com/gz/Util.java" ]; then
    echo "✓ 工具类存在"
else
    echo "✗ 工具类不存在"
fi

if [ -f "gz-api-demo/src/main/java/com/gz/queryValidImei.java" ]; then
    echo "✓ IMEI查询基础版本已创建"
else
    echo "✗ IMEI查询基础版本未创建"
fi

if [ -f "gz-api-demo/src/main/java/com/gz/queryValidImeiManual.java" ]; then
    echo "✓ IMEI查询手动输入版本已创建"
else
    echo "✗ IMEI查询手动输入版本未创建"
fi
echo

# 检查依赖配置
echo "4. 检查依赖配置..."
if grep -q "hutool-all" gz-api-demo/pom.xml; then
    echo "✓ Hutool依赖已配置"
else
    echo "✗ Hutool依赖未配置"
fi

if grep -q "fastjson2" gz-api-demo/pom.xml; then
    echo "✓ FastJSON2依赖已配置"
else
    echo "✗ FastJSON2依赖未配置"
fi
echo

# 提供使用说明
echo "5. 使用说明..."
echo "如果环境检查通过，可以按以下步骤运行："
echo
echo "编译项目:"
echo "  cd gz-api-demo"
echo "  mvn compile"
echo
echo "运行基础版本:"
echo "  mvn exec:java -Dexec.mainClass=\"com.gz.queryValidImei\""
echo
echo "运行手动输入版本:"
echo "  mvn exec:java -Dexec.mainClass=\"com.gz.queryValidImeiManual\""
echo
echo "或者使用IDE直接运行main方法"
echo

echo "=== 验证完成 ==="

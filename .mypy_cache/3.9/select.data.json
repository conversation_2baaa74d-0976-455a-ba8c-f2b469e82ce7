{".class": "MypyFile", "_fullname": "select", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "FileDescriptorLike": {".class": "SymbolTableNode", "cross_ref": "_typeshed.FileDescriptorLike", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "KQ_EV_ADD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "select.KQ_EV_ADD", "name": "KQ_EV_ADD", "type": "builtins.int"}}, "KQ_EV_CLEAR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "select.KQ_EV_CLEAR", "name": "KQ_EV_CLEAR", "type": "builtins.int"}}, "KQ_EV_DELETE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "select.KQ_EV_DELETE", "name": "KQ_EV_DELETE", "type": "builtins.int"}}, "KQ_EV_DISABLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "select.KQ_EV_DISABLE", "name": "KQ_EV_DISABLE", "type": "builtins.int"}}, "KQ_EV_ENABLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "select.KQ_EV_ENABLE", "name": "KQ_EV_ENABLE", "type": "builtins.int"}}, "KQ_EV_EOF": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "select.KQ_EV_EOF", "name": "KQ_EV_EOF", "type": "builtins.int"}}, "KQ_EV_ERROR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "select.KQ_EV_ERROR", "name": "KQ_EV_ERROR", "type": "builtins.int"}}, "KQ_EV_FLAG1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "select.KQ_EV_FLAG1", "name": "KQ_EV_FLAG1", "type": "builtins.int"}}, "KQ_EV_ONESHOT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "select.KQ_EV_ONESHOT", "name": "KQ_EV_ONESHOT", "type": "builtins.int"}}, "KQ_EV_SYSFLAGS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "select.KQ_EV_SYSFLAGS", "name": "KQ_EV_SYSFLAGS", "type": "builtins.int"}}, "KQ_FILTER_AIO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "select.KQ_FILTER_AIO", "name": "KQ_FILTER_AIO", "type": "builtins.int"}}, "KQ_FILTER_PROC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "select.KQ_FILTER_PROC", "name": "KQ_FILTER_PROC", "type": "builtins.int"}}, "KQ_FILTER_READ": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "select.KQ_FILTER_READ", "name": "KQ_FILTER_READ", "type": "builtins.int"}}, "KQ_FILTER_SIGNAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "select.KQ_FILTER_SIGNAL", "name": "KQ_FILTER_SIGNAL", "type": "builtins.int"}}, "KQ_FILTER_TIMER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "select.KQ_FILTER_TIMER", "name": "KQ_FILTER_TIMER", "type": "builtins.int"}}, "KQ_FILTER_VNODE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "select.KQ_FILTER_VNODE", "name": "KQ_FILTER_VNODE", "type": "builtins.int"}}, "KQ_FILTER_WRITE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "select.KQ_FILTER_WRITE", "name": "KQ_FILTER_WRITE", "type": "builtins.int"}}, "KQ_NOTE_ATTRIB": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "select.KQ_NOTE_ATTRIB", "name": "KQ_NOTE_ATTRIB", "type": "builtins.int"}}, "KQ_NOTE_CHILD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "select.KQ_NOTE_CHILD", "name": "KQ_NOTE_CHILD", "type": "builtins.int"}}, "KQ_NOTE_DELETE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "select.KQ_NOTE_DELETE", "name": "KQ_NOTE_DELETE", "type": "builtins.int"}}, "KQ_NOTE_EXEC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "select.KQ_NOTE_EXEC", "name": "KQ_NOTE_EXEC", "type": "builtins.int"}}, "KQ_NOTE_EXIT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "select.KQ_NOTE_EXIT", "name": "KQ_NOTE_EXIT", "type": "builtins.int"}}, "KQ_NOTE_EXTEND": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "select.KQ_NOTE_EXTEND", "name": "KQ_NOTE_EXTEND", "type": "builtins.int"}}, "KQ_NOTE_FORK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "select.KQ_NOTE_FORK", "name": "KQ_NOTE_FORK", "type": "builtins.int"}}, "KQ_NOTE_LINK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "select.KQ_NOTE_LINK", "name": "KQ_NOTE_LINK", "type": "builtins.int"}}, "KQ_NOTE_LOWAT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "select.KQ_NOTE_LOWAT", "name": "KQ_NOTE_LOWAT", "type": "builtins.int"}}, "KQ_NOTE_PCTRLMASK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "select.KQ_NOTE_PCTRLMASK", "name": "KQ_NOTE_PCTRLMASK", "type": "builtins.int"}}, "KQ_NOTE_PDATAMASK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "select.KQ_NOTE_PDATAMASK", "name": "KQ_NOTE_PDATAMASK", "type": "builtins.int"}}, "KQ_NOTE_RENAME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "select.KQ_NOTE_RENAME", "name": "KQ_NOTE_RENAME", "type": "builtins.int"}}, "KQ_NOTE_REVOKE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "select.KQ_NOTE_REVOKE", "name": "KQ_NOTE_REVOKE", "type": "builtins.int"}}, "KQ_NOTE_TRACK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "select.KQ_NOTE_TRACK", "name": "KQ_NOTE_TRACK", "type": "builtins.int"}}, "KQ_NOTE_TRACKERR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "select.KQ_NOTE_TRACKERR", "name": "KQ_NOTE_TRACKERR", "type": "builtins.int"}}, "KQ_NOTE_WRITE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "select.KQ_NOTE_WRITE", "name": "KQ_NOTE_WRITE", "type": "builtins.int"}}, "PIPE_BUF": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "select.PIPE_BUF", "name": "PIPE_BUF", "type": "builtins.int"}}, "POLLERR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "select.POLLERR", "name": "POLLERR", "type": "builtins.int"}}, "POLLHUP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "select.POLLHUP", "name": "POLLHUP", "type": "builtins.int"}}, "POLLIN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "select.POLLIN", "name": "POLLIN", "type": "builtins.int"}}, "POLLNVAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "select.POLLNVAL", "name": "POLLNVAL", "type": "builtins.int"}}, "POLLOUT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "select.POLLOUT", "name": "POLLOUT", "type": "builtins.int"}}, "POLLPRI": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "select.POLLPRI", "name": "POLLPRI", "type": "builtins.int"}}, "POLLRDBAND": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "select.POLLRDBAND", "name": "POLLRDBAND", "type": "builtins.int"}}, "POLLRDNORM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "select.POLLRDNORM", "name": "POLLRDNORM", "type": "builtins.int"}}, "POLLWRBAND": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "select.POLLWRBAND", "name": "POLLWRBAND", "type": "builtins.int"}}, "POLLWRNORM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "select.POLLWRNORM", "name": "POLLWRNORM", "type": "builtins.int"}}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Self", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TracebackType": {".class": "SymbolTableNode", "cross_ref": "types.TracebackType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "select.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "select.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "select.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "select.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "select.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "select.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "error": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "select.error", "line": 38, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.OSError"}}, "final": {".class": "SymbolTableNode", "cross_ref": "typing.final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "kevent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "select.kevent", "name": "kevent", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "select.kevent", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "select", "mro": ["select.kevent", "builtins.object"], "names": {".class": "SymbolTable", "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "select.kevent.__hash__", "name": "__hash__", "type": {".class": "NoneType"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "ident", "filter", "flags", "fflags", "data", "udata"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "select.kevent.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "ident", "filter", "flags", "fflags", "data", "udata"], "arg_types": ["select.kevent", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.FileDescriptorLike"}, "builtins.int", "builtins.int", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of kevent", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "select.kevent.data", "name": "data", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "fflags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "select.kevent.fflags", "name": "fflags", "type": "builtins.int"}}, "filter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "select.kevent.filter", "name": "filter", "type": "builtins.int"}}, "flags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "select.kevent.flags", "name": "flags", "type": "builtins.int"}}, "ident": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "select.kevent.ident", "name": "ident", "type": "builtins.int"}}, "udata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "select.kevent.udata", "name": "udata", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "select.kevent.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "select.kevent", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "kqueue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "select.kqueue", "name": "kqueue", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "select.kqueue", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "select", "mro": ["select.kqueue", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "select.kqueue.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["select.kqueue"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of kqueue", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "select.kqueue.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["select.kqueue"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of kqueue", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "closed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "select.kqueue.closed", "name": "closed", "type": "builtins.bool"}}, "control": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "select.kqueue.control", "name": "control", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": [null, null, null, null], "arg_types": ["select.kqueue", {".class": "UnionType", "items": [{".class": "Instance", "args": ["select.kevent"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "control of kqueue", "ret_type": {".class": "Instance", "args": ["select.kevent"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fileno": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "select.kqueue.fileno", "name": "fileno", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["select.kqueue"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fileno of kqueue", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fromfd": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "select.kqueue.fromfd", "name": "fromfd", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeType", "item": "select.kqueue"}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.FileDescriptorLike"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fromfd of kqueue", "ret_type": "select.kqueue", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "select.kqueue.fromfd", "name": "fromfd", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeType", "item": "select.kqueue"}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.FileDescriptorLike"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fromfd of kqueue", "ret_type": "select.kqueue", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "select.kqueue.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "select.kqueue", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "poll": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "select.poll", "name": "poll", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "select.poll", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "select", "mro": ["select.poll", "builtins.object"], "names": {".class": "SymbolTable", "modify": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "select.poll.modify", "name": "modify", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["select.poll", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.FileDescriptorLike"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "modify of poll", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "poll": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "select.poll.poll", "name": "poll", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": [null, null], "arg_types": ["select.poll", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "poll of poll", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "register": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "select.poll.register", "name": "register", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": [null, null, null], "arg_types": ["select.poll", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.FileDescriptorLike"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register of poll", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "unregister": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "select.poll.unregister", "name": "unregister", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["select.poll", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.FileDescriptorLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unregister of poll", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "select.poll.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "select.poll", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "select": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "select.select", "name": "select", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": [null, null, null, null], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "select", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "/Users/<USER>/.vscode/extensions/ms-python.mypy-type-checker-2025.2.0/bundled/libs/mypy/typeshed/stdlib/select.pyi"}
{".class": "MypyFile", "_fullname": "errno", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "E2BIG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.E2BIG", "name": "E2BIG", "type": "builtins.int"}}, "EACCES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EACCES", "name": "EACCES", "type": "builtins.int"}}, "EADDRINUSE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EADDRINUSE", "name": "EADDRINUSE", "type": "builtins.int"}}, "EADDRNOTAVAIL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EADDRNOTAVAIL", "name": "EADDRNOTAVAIL", "type": "builtins.int"}}, "EAFNOSUPPORT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EAFNOSUPPORT", "name": "EAFNOSUPPORT", "type": "builtins.int"}}, "EAGAIN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EAGAIN", "name": "EAGAIN", "type": "builtins.int"}}, "EALREADY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EALREADY", "name": "EALREADY", "type": "builtins.int"}}, "EAUTH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EAUTH", "name": "EAUTH", "type": "builtins.int"}}, "EBADARCH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EBADARCH", "name": "EBADARCH", "type": "builtins.int"}}, "EBADEXEC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EBADEXEC", "name": "EBADEXEC", "type": "builtins.int"}}, "EBADF": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EBADF", "name": "EBADF", "type": "builtins.int"}}, "EBADMACHO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EBADMACHO", "name": "EBADMACHO", "type": "builtins.int"}}, "EBADMSG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EBADMSG", "name": "EBADMSG", "type": "builtins.int"}}, "EBADRPC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EBADRPC", "name": "EBADRPC", "type": "builtins.int"}}, "EBUSY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EBUSY", "name": "EBUSY", "type": "builtins.int"}}, "ECANCELED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ECANCELED", "name": "ECANCELED", "type": "builtins.int"}}, "ECHILD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ECHILD", "name": "ECHILD", "type": "builtins.int"}}, "ECONNABORTED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ECONNABORTED", "name": "ECONNABORTED", "type": "builtins.int"}}, "ECONNREFUSED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ECONNREFUSED", "name": "ECONNREFUSED", "type": "builtins.int"}}, "ECONNRESET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ECONNRESET", "name": "ECONNRESET", "type": "builtins.int"}}, "EDEADLK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EDEADLK", "name": "EDEADLK", "type": "builtins.int"}}, "EDESTADDRREQ": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EDESTADDRREQ", "name": "EDESTADDRREQ", "type": "builtins.int"}}, "EDEVERR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EDEVERR", "name": "EDEVERR", "type": "builtins.int"}}, "EDOM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EDOM", "name": "EDOM", "type": "builtins.int"}}, "EDQUOT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EDQUOT", "name": "EDQUOT", "type": "builtins.int"}}, "EEXIST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EEXIST", "name": "EEXIST", "type": "builtins.int"}}, "EFAULT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EFAULT", "name": "EFAULT", "type": "builtins.int"}}, "EFBIG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EFBIG", "name": "EFBIG", "type": "builtins.int"}}, "EFTYPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EFTYPE", "name": "EFTYPE", "type": "builtins.int"}}, "EHOSTDOWN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EHOSTDOWN", "name": "EHOSTDOWN", "type": "builtins.int"}}, "EHOSTUNREACH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EHOSTUNREACH", "name": "EHOSTUNREACH", "type": "builtins.int"}}, "EIDRM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EIDRM", "name": "EIDRM", "type": "builtins.int"}}, "EILSEQ": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EILSEQ", "name": "EILSEQ", "type": "builtins.int"}}, "EINPROGRESS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EINPROGRESS", "name": "EINPROGRESS", "type": "builtins.int"}}, "EINTR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EINTR", "name": "EINTR", "type": "builtins.int"}}, "EINVAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EINVAL", "name": "EINVAL", "type": "builtins.int"}}, "EIO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EIO", "name": "EIO", "type": "builtins.int"}}, "EISCONN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EISCONN", "name": "EISCONN", "type": "builtins.int"}}, "EISDIR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EISDIR", "name": "EISDIR", "type": "builtins.int"}}, "ELOOP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ELOOP", "name": "ELOOP", "type": "builtins.int"}}, "EMFILE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EMFILE", "name": "EMFILE", "type": "builtins.int"}}, "EMLINK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EMLINK", "name": "EMLINK", "type": "builtins.int"}}, "EMSGSIZE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EMSGSIZE", "name": "EMSGSIZE", "type": "builtins.int"}}, "EMULTIHOP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EMULTIHOP", "name": "EMULTIHOP", "type": "builtins.int"}}, "ENAMETOOLONG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENAMETOOLONG", "name": "ENAMETOOLONG", "type": "builtins.int"}}, "ENEEDAUTH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENEEDAUTH", "name": "ENEEDAUTH", "type": "builtins.int"}}, "ENETDOWN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENETDOWN", "name": "ENETDOWN", "type": "builtins.int"}}, "ENETRESET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENETRESET", "name": "ENETRESET", "type": "builtins.int"}}, "ENETUNREACH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENETUNREACH", "name": "ENETUNREACH", "type": "builtins.int"}}, "ENFILE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENFILE", "name": "ENFILE", "type": "builtins.int"}}, "ENOATTR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOATTR", "name": "ENOATTR", "type": "builtins.int"}}, "ENOBUFS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOBUFS", "name": "ENOBUFS", "type": "builtins.int"}}, "ENODATA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENODATA", "name": "ENODATA", "type": "builtins.int"}}, "ENODEV": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENODEV", "name": "ENODEV", "type": "builtins.int"}}, "ENOENT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOENT", "name": "ENOENT", "type": "builtins.int"}}, "ENOEXEC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOEXEC", "name": "ENOEXEC", "type": "builtins.int"}}, "ENOLCK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOLCK", "name": "ENOLCK", "type": "builtins.int"}}, "ENOLINK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOLINK", "name": "ENOLINK", "type": "builtins.int"}}, "ENOMEM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOMEM", "name": "ENOMEM", "type": "builtins.int"}}, "ENOMSG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOMSG", "name": "ENOMSG", "type": "builtins.int"}}, "ENOPOLICY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOPOLICY", "name": "ENOPOLICY", "type": "builtins.int"}}, "ENOPROTOOPT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOPROTOOPT", "name": "ENOPROTOOPT", "type": "builtins.int"}}, "ENOSPC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOSPC", "name": "ENOSPC", "type": "builtins.int"}}, "ENOSR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOSR", "name": "ENOSR", "type": "builtins.int"}}, "ENOSTR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOSTR", "name": "ENOSTR", "type": "builtins.int"}}, "ENOSYS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOSYS", "name": "ENOSYS", "type": "builtins.int"}}, "ENOTBLK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOTBLK", "name": "ENOTBLK", "type": "builtins.int"}}, "ENOTCONN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOTCONN", "name": "ENOTCONN", "type": "builtins.int"}}, "ENOTDIR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOTDIR", "name": "ENOTDIR", "type": "builtins.int"}}, "ENOTEMPTY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOTEMPTY", "name": "ENOTEMPTY", "type": "builtins.int"}}, "ENOTRECOVERABLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOTRECOVERABLE", "name": "ENOTRECOVERABLE", "type": "builtins.int"}}, "ENOTSOCK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOTSOCK", "name": "ENOTSOCK", "type": "builtins.int"}}, "ENOTSUP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOTSUP", "name": "ENOTSUP", "type": "builtins.int"}}, "ENOTTY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENOTTY", "name": "ENOTTY", "type": "builtins.int"}}, "ENXIO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ENXIO", "name": "ENXIO", "type": "builtins.int"}}, "EOPNOTSUPP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EOPNOTSUPP", "name": "EOPNOTSUPP", "type": "builtins.int"}}, "EOVERFLOW": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EOVERFLOW", "name": "EOVERFLOW", "type": "builtins.int"}}, "EOWNERDEAD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EOWNERDEAD", "name": "EOWNERDEAD", "type": "builtins.int"}}, "EPERM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EPERM", "name": "EPERM", "type": "builtins.int"}}, "EPFNOSUPPORT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EPFNOSUPPORT", "name": "EPFNOSUPPORT", "type": "builtins.int"}}, "EPIPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EPIPE", "name": "EPIPE", "type": "builtins.int"}}, "EPROCLIM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EPROCLIM", "name": "EPROCLIM", "type": "builtins.int"}}, "EPROCUNAVAIL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EPROCUNAVAIL", "name": "EPROCUNAVAIL", "type": "builtins.int"}}, "EPROGMISMATCH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EPROGMISMATCH", "name": "EPROGMISMATCH", "type": "builtins.int"}}, "EPROGUNAVAIL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EPROGUNAVAIL", "name": "EPROGUNAVAIL", "type": "builtins.int"}}, "EPROTO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EPROTO", "name": "EPROTO", "type": "builtins.int"}}, "EPROTONOSUPPORT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EPROTONOSUPPORT", "name": "EPROTONOSUPPORT", "type": "builtins.int"}}, "EPROTOTYPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EPROTOTYPE", "name": "EPROTOTYPE", "type": "builtins.int"}}, "EPWROFF": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EPWROFF", "name": "EPWROFF", "type": "builtins.int"}}, "ERANGE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ERANGE", "name": "ERANGE", "type": "builtins.int"}}, "EREMOTE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EREMOTE", "name": "EREMOTE", "type": "builtins.int"}}, "EROFS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EROFS", "name": "EROFS", "type": "builtins.int"}}, "ERPCMISMATCH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ERPCMISMATCH", "name": "ERPCMISMATCH", "type": "builtins.int"}}, "ESHLIBVERS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ESHLIBVERS", "name": "ESHLIBVERS", "type": "builtins.int"}}, "ESHUTDOWN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ESHUTDOWN", "name": "ESHUTDOWN", "type": "builtins.int"}}, "ESOCKTNOSUPPORT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ESOCKTNOSUPPORT", "name": "ESOCKTNOSUPPORT", "type": "builtins.int"}}, "ESPIPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ESPIPE", "name": "ESPIPE", "type": "builtins.int"}}, "ESRCH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ESRCH", "name": "ESRCH", "type": "builtins.int"}}, "ESTALE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ESTALE", "name": "ESTALE", "type": "builtins.int"}}, "ETIME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ETIME", "name": "ETIME", "type": "builtins.int"}}, "ETIMEDOUT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ETIMEDOUT", "name": "ETIMEDOUT", "type": "builtins.int"}}, "ETOOMANYREFS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ETOOMANYREFS", "name": "ETOOMANYREFS", "type": "builtins.int"}}, "ETXTBSY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.ETXTBSY", "name": "ETXTBSY", "type": "builtins.int"}}, "EUSERS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EUSERS", "name": "EUSERS", "type": "builtins.int"}}, "EWOULDBLOCK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EWOULDBLOCK", "name": "EWOULDBLOCK", "type": "builtins.int"}}, "EXDEV": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.EXDEV", "name": "EXDEV", "type": "builtins.int"}}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "errorcode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "errno.errorcode", "name": "errorcode", "type": {".class": "Instance", "args": ["builtins.int", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "/Users/<USER>/.vscode/extensions/ms-python.mypy-type-checker-2025.2.0/bundled/libs/mypy/typeshed/stdlib/errno.pyi"}
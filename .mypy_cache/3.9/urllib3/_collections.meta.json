{"data_mtime": 1758628801, "dep_lines": [1, 3, 4, 5, 6, 13, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 25, 5, 30, 30, 30, 30], "dependencies": ["__future__", "typing", "collections", "enum", "threading", "typing_extensions", "builtins", "_frozen_importlib", "_thread", "_typeshed", "abc"], "hash": "68262ad30321eacb6b5bc9952c9ada71e7f602b4", "id": "urllib3._collections", "ignore_all": true, "interface_hash": "8e0dd6d45877959c94ee73f1619ecbb53f2f177b", "mtime": 1755227896, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/.pyenv/versions/3.9.18/lib/python3.9/site-packages/urllib3/_collections.py", "plugin_data": null, "size": 17295, "suppressed": [], "version_id": "1.15.0"}
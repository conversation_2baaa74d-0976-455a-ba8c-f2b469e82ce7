{"data_mtime": 1758628801, "dep_lines": [9, 1, 3, 4, 5, 6, 7, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 10, 10, 5, 5, 30, 30], "dependencies": ["urllib3.fields", "__future__", "<PERSON><PERSON><PERSON><PERSON>", "codecs", "os", "typing", "io", "builtins", "_frozen_importlib", "abc"], "hash": "963087efaa05111fddcd35b6ec79a3ef0f489d46", "id": "urllib3.filepost", "ignore_all": true, "interface_hash": "619891d256457628658cd80310a4f1800d1fc7f1", "mtime": 1755227896, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/.pyenv/versions/3.9.18/lib/python3.9/site-packages/urllib3/filepost.py", "plugin_data": null, "size": 2388, "suppressed": [], "version_id": "1.15.0"}
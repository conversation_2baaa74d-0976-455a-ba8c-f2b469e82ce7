{".class": "MypyFile", "_fullname": "urllib3.util.response", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "HeaderParsingError": {".class": "SymbolTableNode", "cross_ref": "urllib3.exceptions.HeaderParsingError", "kind": "Gdef"}, "MultipartInvariantViolationDefect": {".class": "SymbolTableNode", "cross_ref": "email.errors.MultipartInvariantViolationDefect", "kind": "Gdef"}, "StartBoundaryNotFoundDefect": {".class": "SymbolTableNode", "cross_ref": "email.errors.StartBoundaryNotFoundDefect", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.util.response.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.util.response.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.util.response.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.util.response.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.util.response.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.util.response.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "assert_header_parsing": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.util.response.assert_header_parsing", "name": "assert_header_parsing", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["headers"], "arg_types": ["http.client.HTTPMessage"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_header_parsing", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "httplib": {".class": "SymbolTableNode", "cross_ref": "http.client", "kind": "Gdef"}, "is_fp_closed": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.util.response.is_fp_closed", "name": "is_fp_closed", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": ["builtins.object"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_fp_closed", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_response_to_head": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["response"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.util.response.is_response_to_head", "name": "is_response_to_head", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["response"], "arg_types": ["http.client.HTTPResponse"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_response_to_head", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "/Users/<USER>/.pyenv/versions/3.9.18/lib/python3.9/site-packages/urllib3/util/response.py"}
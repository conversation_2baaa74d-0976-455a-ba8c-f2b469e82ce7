{"data_mtime": 1758628801, "dep_lines": [5, 6, 7, 33, 1, 3, 30, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 25, 5, 10, 25, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["urllib3.util.connection", "urllib3.util.timeout", "urllib3.util.url", "urllib3.response", "__future__", "typing", "ssl", "builtins", "_frozen_importlib", "_io", "_ssl", "abc", "enum", "io", "urllib3.util"], "hash": "e8757cc19f192364558bc3fe999cb39dd3bda487", "id": "urllib3._base_connection", "ignore_all": true, "interface_hash": "691ee8ded2b535381cc767f629d36895739137f7", "mtime": 1755227896, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/.pyenv/versions/3.9.18/lib/python3.9/site-packages/urllib3/_base_connection.py", "plugin_data": null, "size": 5568, "suppressed": [], "version_id": "1.15.0"}
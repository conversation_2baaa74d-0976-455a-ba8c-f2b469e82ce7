{"data_mtime": 1758628801, "dep_lines": [3, 1, 3, 4, 5, 187, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 20, 10, 10, 20, 5, 30, 30, 30, 30], "dependencies": ["email.utils", "__future__", "email", "mimetypes", "typing", "warnings", "builtins", "_frozen_importlib", "_typeshed", "_warnings", "abc"], "hash": "c3284679bbe778a3637ea498e55953ff2092d35c", "id": "urllib3.fields", "ignore_all": true, "interface_hash": "82182ce7c7c063807b9a1623a257a76eec56d6ab", "mtime": 1755227896, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/.pyenv/versions/3.9.18/lib/python3.9/site-packages/urllib3/fields.py", "plugin_data": null, "size": 10829, "suppressed": [], "version_id": "1.15.0"}
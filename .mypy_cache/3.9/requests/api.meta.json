{"data_mtime": 1758628801, "dep_lines": [1, 2, 5, 6, 3, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["collections.abc", "http.cookiejar", "requests.models", "requests.sessions", "typing_extensions", "builtins", "_frozen_importlib", "_typeshed", "abc", "http", "requests.auth", "typing"], "hash": "6a94ada508e5a8edcc1222b603a4d1b578821c68", "id": "requests.api", "ignore_all": true, "interface_hash": "9b47926b91698744d791372169a396219c8c63b7", "mtime": 1755833491, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/.pyenv/versions/3.9.18/lib/python3.9/site-packages/requests-stubs/api.pyi", "plugin_data": null, "size": 4763, "suppressed": [], "version_id": "1.15.0"}
{"data_mtime": 1758628801, "dep_lines": [3, 3, 3, 1, 3, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 20, 5, 30, 30], "dependencies": ["requests.cookies", "requests.models", "requests.utils", "typing", "requests", "builtins", "_frozen_importlib", "abc"], "hash": "8ba61b259810686b2f38876c8ecc04d3370d2ad0", "id": "requests.auth", "ignore_all": true, "interface_hash": "68dc48cbc0bd64f446de761d2aae51e2cd77a71f", "mtime": 1755833491, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/.pyenv/versions/3.9.18/lib/python3.9/site-packages/requests-stubs/auth.pyi", "plugin_data": null, "size": 1191, "suppressed": [], "version_id": "1.15.0"}
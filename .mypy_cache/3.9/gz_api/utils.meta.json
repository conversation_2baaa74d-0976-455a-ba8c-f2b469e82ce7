{"data_mtime": 1758628847, "dep_lines": [6, 7, 8, 9, 10, 11, 1, 1, 1, 1, 12], "dep_prios": [10, 10, 10, 10, 5, 10, 5, 30, 30, 30, 5], "dependencies": ["<PERSON><PERSON><PERSON>", "json", "re", "time", "typing", "requests", "builtins", "_frozen_importlib", "abc", "enum"], "hash": "1873db883cd2e5617cd8ea0e3e572ccf39e1d1ee", "id": "gz_api.utils", "ignore_all": true, "interface_hash": "d90047d432b47bf65709ca9a578ee292f83f8ca6", "mtime": 1758628799, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Desktop/Project Workspace/GZ/gz-api-python/gz_api/utils.py", "plugin_data": null, "size": 5416, "suppressed": ["colorama"], "version_id": "1.15.0"}
{"data_mtime": 1758628847, "dep_lines": [7, 6, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 30, 30, 30], "dependencies": ["gz_api.utils", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc"], "hash": "59c33f6cdd18b3dcfef96a8cb06d347420decfe1", "id": "gz_api.response_parser", "ignore_all": false, "interface_hash": "1c0c0641b5ef411bda038647ca5aa8ec3e208206", "mtime": 1758628846, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Desktop/Project Workspace/GZ/gz-api-python/gz_api/response_parser.py", "plugin_data": null, "size": 8471, "suppressed": [], "version_id": "1.15.0"}
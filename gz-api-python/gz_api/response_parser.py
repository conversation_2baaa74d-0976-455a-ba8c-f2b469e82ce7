"""
响应解析器
提供格式化的响应结果解析和显示功能
"""

from typing import Dict, Any, List, Optional
from .utils import GzApiUtils

class ResponseParser:
    """响应解析器类"""
    
    @staticmethod
    def parse_basic_response(response_data: Dict[str, Any]) -> None:
        """解析基本响应信息"""
        if not response_data.get('success'):
            GzApiUtils.print_error(f"请求失败: {response_data.get('error', '未知错误')}")
            return
        
        response_json = response_data.get('response_json')
        response_text = response_data.get('response_text', '')
        
        print(f"\n原始响应数据: {response_text}")
        
        if not response_json:
            if '404' in response_text or 'Not Found' in response_text or 'Whitelabel Error Page' in response_text:
                GzApiUtils.print_section("接口不可用 (404错误)")
                GzApiUtils.print_error("接口暂时不可用")
                GzApiUtils.print_info("可能原因:")
                print("   1. 接口尚未在服务器端实现")
                print("   2. 接口路径可能不正确")
                print("   3. 该功能可能需要特殊权限")
            else:
                GzApiUtils.print_warning("响应格式: 非JSON格式")
                print(f"响应内容: {response_text}")
            return
        
        GzApiUtils.print_section("格式化结果")
        
        # 解析基本信息
        code = response_json.get('code')
        msg = response_json.get('msg', '')
        success = response_json.get('success', False)
        
        GzApiUtils.print_info("接口调用状态:")
        status_text = "成功" if code == 0 else "失败"
        print(f"   状态码: {code} ({status_text})")
        print(f"   消息: {msg}")
        print(f"   成功标识: {success}")
        
        return response_json
    
    @staticmethod
    def parse_settlement_response(response_data: Dict[str, Any]) -> None:
        """解析结清订单响应"""
        response_json = ResponseParser.parse_basic_response(response_data)
        if not response_json:
            return
        
        code = response_json.get('code')
        msg = response_json.get('msg', '')
        success = response_json.get('success', False)
        
        if code == 0 and success:
            print(f"\n🎉 结清结果:")
            GzApiUtils.print_success("公证订单结清成功")
            GzApiUtils.print_success("订单状态已更新为结清状态")
            
            print(f"\n📝 说明:")
            print("   - 订单结清后将无法再进行相关操作")
            print("   - 结清操作不可逆，请确认订单信息正确")
            print("   - 结清后相关设备和资源将被释放")
        else:
            print(f"\n❌ 结清结果:")
            GzApiUtils.print_error("公证订单结清失败")
            print(f"   原因: {msg}")
            
            print(f"\n💡 可能原因:")
            print("   - 订单号不存在")
            print("   - 订单状态不允许结清")
            print("   - 订单已经结清")
            print("   - 权限不足")
    
    @staticmethod
    def parse_order_query_response(response_data: Dict[str, Any]) -> None:
        """解析用户订单查询响应"""
        response_json = ResponseParser.parse_basic_response(response_data)
        if not response_json:
            return
        
        code = response_json.get('code')
        success = response_json.get('success', False)
        
        if code == 0 and success:
            data = response_json.get('data', {})
            is_certified = data.get('isCertified', 0)
            goods_num = data.get('goodsNum', 0)
            
            print(f"\n📊 用户订单信息:")
            print(f"   有效公证订单数: {is_certified} 个")
            print(f"   在租台数: {goods_num} 台")
            
            print(f"\n🔍 业务状态分析:")
            if goods_num == 0:
                GzApiUtils.print_success("用户当前没有在租设备")
                GzApiUtils.print_success("可以申请新的公证服务")
            elif goods_num >= 5:
                GzApiUtils.print_warning("用户已达到最大设备数量限制")
                GzApiUtils.print_warning("无法申请更多设备公证")
            else:
                GzApiUtils.print_info(f"用户当前在租 {goods_num} 台设备")
                GzApiUtils.print_success(f"还可以申请 {5 - goods_num} 台设备")
            
            if is_certified == 0:
                print("   📝 用户当前没有有效的公证订单")
            else:
                print(f"   📝 用户有 {is_certified} 个有效公证订单")
            
            print(f"\n📖 字段说明:")
            print("   - isCertified: 用户有效公证订单数")
            print("   - goodsNum: 用户在租台数（最多5台设备）")
    
    @staticmethod
    def parse_imei_query_response(response_data: Dict[str, Any], queried_imeis: List[str]) -> None:
        """解析IMEI查询响应"""
        response_json = ResponseParser.parse_basic_response(response_data)
        if not response_json:
            return
        
        code = response_json.get('code')
        success = response_json.get('success', False)
        
        if code == 0 and success:
            data = response_json.get('data', {})
            duplicate_imeis = data.get('imeiCodes', [])
            
            print(f"\n📱 IMEI查询结果:")
            print(f"   查询的IMEI数量: {len(queried_imeis)} 个")
            
            if duplicate_imeis:
                print(f"   重复的IMEI数量: {len(duplicate_imeis)} 个")
                print(f"   重复的IMEI编码: {', '.join(duplicate_imeis)}")
                
                print(f"\n🔍 验证结果:")
                GzApiUtils.print_error("发现重复的IMEI编码")
                GzApiUtils.print_error("这些IMEI编码不可用于申请公证")
                
                print(f"\n✅ 可用的IMEI编码:")
                for imei in queried_imeis:
                    if imei in duplicate_imeis:
                        GzApiUtils.print_error(f"{imei} (重复，不可用)")
                    else:
                        GzApiUtils.print_success(f"{imei} (可用)")
            else:
                print(f"   重复的IMEI数量: 0 个")
                
                print(f"\n🔍 验证结果:")
                GzApiUtils.print_success("所有IMEI编码都可用")
                GzApiUtils.print_success("可以继续申请公证")
                
                print(f"\n✅ 可用的IMEI编码:")
                for imei in queried_imeis:
                    GzApiUtils.print_success(f"{imei} (可用)")
            
            print(f"\n📖 业务说明:")
            print("   - 返回的imeiCodes数组包含重复的IMEI编码")
            print("   - 如果数组为空，表示所有IMEI都可用")
            print("   - 重复的IMEI不可用于申请公证")
    
    @staticmethod
    def parse_imei_push_response(response_data: Dict[str, Any]) -> None:
        """解析IMEI推送响应"""
        response_json = ResponseParser.parse_basic_response(response_data)
        if not response_json:
            return
        
        code = response_json.get('code')
        msg = response_json.get('msg', '')
        success = response_json.get('success', False)
        
        if code == 0 and success:
            print(f"\n🎉 推送结果:")
            GzApiUtils.print_success("IMEI编码推送成功")
            GzApiUtils.print_success("设备信息已记录到公证系统")
            
            print(f"\n📝 说明:")
            print("   - 此接口在公证书状态为'视频已通过'时调用")
            print("   - 用于将设备IMEI编码推送到公证系统")
            print("   - 推送成功后设备信息将被永久记录")
        else:
            print(f"\n❌ 推送结果:")
            GzApiUtils.print_error("IMEI编码推送失败")
            print(f"   原因: {msg}")
            
            print(f"\n💡 可能原因:")
            print("   - 订单号不存在")
            print("   - 订单状态不是'视频已通过'")
            print("   - IMEI编码格式错误")
            print("   - 权限不足")

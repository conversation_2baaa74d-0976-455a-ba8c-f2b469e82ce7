#!/usr/bin/env python3
"""
1.10 用户有效订单查询
查询用户的有效公证订单数和在租台数
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from gz_api.utils import GzApiUtils
from gz_api.response_parser import ResponseParser

def all_running_order_query_basic():
    """用户有效订单查询 - 基础版本"""
    # 构建请求参数
    params = {
        'applicantID': '350521199812164511'  # 申请公证身份证号
    }
    
    GzApiUtils.print_section("用户有效订单查询")
    print(f"商户号: {GzApiUtils.MERCHANT_NO}")
    print(f"申请公证身份证号: {params['applicantID']}")
    print(f"请求参数: {params}")
    
    # 发送请求
    response_data = GzApiUtils.send_request('gzs/allRunningOrderQuery', params)
    
    # 解析响应
    ResponseParser.parse_order_query_response(response_data)

def all_running_order_query_manual():
    """用户有效订单查询 - 手动输入版本"""
    GzApiUtils.print_section("用户有效订单查询 - 手动输入模式")
    print(f"商户号: {GzApiUtils.MERCHANT_NO}")
    print("功能说明: 查询用户的有效公证订单数和在租台数")
    print("注意: 公证处支持每人最多5台设备")
    print()
    
    try:
        # 获取用户输入
        while True:
            applicant_id = GzApiUtils.get_input_with_default(
                "请输入申请公证身份证号（18位）", 
                "350521199812164511"
            )
            
            # 验证身份证号格式
            if GzApiUtils.validate_id_card(applicant_id):
                break
            else:
                GzApiUtils.print_error("错误：身份证号必须是18位，最后一位可以是X，请重新输入！")
        
        # 构建请求参数
        params = {
            'applicantID': applicant_id
        }
        
        GzApiUtils.print_section("请求信息")
        print(f"申请公证身份证号: {applicant_id}")
        print(f"请求参数: {params}")
        
        # 确认发送请求
        if GzApiUtils.confirm_action("\n是否发送请求？"):
            GzApiUtils.print_section("发送请求")
            
            # 发送请求
            response_data = GzApiUtils.send_request('gzs/allRunningOrderQuery', params)
            
            # 解析响应
            ResponseParser.parse_order_query_response(response_data)
        else:
            print("已取消请求发送")
            
    except KeyboardInterrupt:
        print("\n\n操作已取消")
    except Exception as e:
        GzApiUtils.print_error(f"程序执行失败: {str(e)}")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "manual":
        all_running_order_query_manual()
    else:
        all_running_order_query_basic()

#!/usr/bin/env python3
"""
1.11 查询有效的IMEI编码
批量查询IMEI编码是否重复
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from gz_api.utils import GzApiUtils
from gz_api.response_parser import ResponseParser

def query_valid_imei_basic():
    """查询有效的IMEI编码 - 基础版本"""
    # 构建请求参数 - 支持批量查询
    imei_codes = ['123456789012345', '355011479595868']
    params = {
        'imeiCodes': imei_codes
    }
    
    GzApiUtils.print_section("查询有效的IMEI编码")
    print(f"商户号: {GzApiUtils.MERCHANT_NO}")
    print(f"查询的IMEI编码: {', '.join(imei_codes)}")
    print(f"请求参数: {params}")
    
    # 发送请求
    response_data = GzApiUtils.send_request('gzs/allImeiCodeQuery', params)
    
    # 解析响应
    ResponseParser.parse_imei_query_response(response_data, imei_codes)

def query_valid_imei_manual():
    """查询有效的IMEI编码 - 手动输入版本"""
    GzApiUtils.print_section("查询有效的IMEI编码 - 手动输入模式")
    print(f"商户号: {GzApiUtils.MERCHANT_NO}")
    print("功能说明: 批量查询IMEI编码是否重复，支持多个IMEI同时查询")
    print()
    
    try:
        # 获取用户输入的IMEI编码列表
        print("请输入要查询的IMEI编码（支持多个，用逗号分隔）:")
        imei_input = GzApiUtils.get_input_with_default(
            "IMEI编码", 
            "123456789012345,355011479595868"
        )
        
        # 解析并验证IMEI编码
        valid_imeis = GzApiUtils.parse_imei_list(imei_input)
        
        if not valid_imeis:
            GzApiUtils.print_error("没有有效的IMEI编码，使用默认值")
            valid_imeis = ['123456789012345', '355011479595868']
        
        # 构建请求参数
        params = {
            'imeiCodes': valid_imeis
        }
        
        GzApiUtils.print_section("请求信息")
        print(f"查询的IMEI编码: {', '.join(valid_imeis)}")
        print(f"请求参数: {params}")
        
        # 确认发送请求
        if GzApiUtils.confirm_action("\n是否发送请求？"):
            GzApiUtils.print_section("发送请求")
            
            # 发送请求
            response_data = GzApiUtils.send_request('gzs/allImeiCodeQuery', params)
            
            # 解析响应
            ResponseParser.parse_imei_query_response(response_data, valid_imeis)
        else:
            print("已取消请求发送")
            
    except KeyboardInterrupt:
        print("\n\n操作已取消")
    except Exception as e:
        GzApiUtils.print_error(f"程序执行失败: {str(e)}")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "manual":
        query_valid_imei_manual()
    else:
        query_valid_imei_basic()

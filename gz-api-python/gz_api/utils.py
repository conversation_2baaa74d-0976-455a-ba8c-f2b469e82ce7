"""
公证接口工具类
提供MD5签名生成、HTTP请求、响应解析等通用功能
"""

import hashlib
import json
import re
import time
from typing import Dict, Any, Optional, List
import requests
from colorama import Fore, Style, init

# 初始化colorama
init(autoreset=True)

class GzApiUtils:
    """公证接口工具类"""
    
    # 商户配置
    MERCHANT_NO = "NJSJ1738988721355510"
    SECRET_KEY = "ygAlUjvUnvq2KrYCuxzT5616C6lBksxk"
    BASE_URL = "http://buss.haochengda.net:8088"
    
    # 正则表达式
    IMEI_PATTERN = re.compile(r'^\d{15}$')
    ID_CARD_PATTERN = re.compile(r'^\d{17}[\dXx]$')
    
    @staticmethod
    def generate_signature(params: Dict[str, Any], secret_key: str) -> str:
        """
        生成MD5签名
        
        Args:
            params: 请求参数字典
            secret_key: 密钥
            
        Returns:
            MD5签名字符串
        """
        # 按键名排序
        sorted_params = sorted(params.items())
        
        # 构建签名字符串
        sign_str = ""
        for key, value in sorted_params:
            if isinstance(value, list):
                # 处理数组参数
                value_str = json.dumps(value, separators=(',', ':'), ensure_ascii=False)
            else:
                value_str = str(value)
            sign_str += f"{key}={value_str}&"
        
        # 添加密钥
        sign_str += f"key={secret_key}"
        
        # 生成MD5
        md5_hash = hashlib.md5(sign_str.encode('utf-8')).hexdigest()
        return md5_hash
    
    @staticmethod
    def send_request(endpoint: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        发送HTTP请求
        
        Args:
            endpoint: 接口端点
            params: 请求参数
            
        Returns:
            响应结果字典
        """
        # 生成签名
        signature = GzApiUtils.generate_signature(params, GzApiUtils.SECRET_KEY)
        params['signature'] = signature
        
        # 构建请求URL
        url = f"{GzApiUtils.BASE_URL}/{endpoint}"
        
        # 请求头
        headers = {
            'GZ-Merchant-No': GzApiUtils.MERCHANT_NO,
            'GZ-Req-Timestamp': str(int(time.time() * 1000)),
            'Content-Type': 'application/json'
        }
        
        try:
            # 发送POST请求
            response = requests.post(
                url=url,
                json=params,
                headers=headers,
                timeout=30
            )
            
            # 返回响应结果
            return {
                'success': True,
                'status_code': response.status_code,
                'response_text': response.text,
                'response_json': response.json() if response.text.strip().startswith('{') else None
            }
            
        except requests.exceptions.RequestException as e:
            return {
                'success': False,
                'error': str(e),
                'response_text': None,
                'response_json': None
            }
    
    @staticmethod
    def validate_imei(imei: str) -> bool:
        """验证IMEI格式"""
        return bool(GzApiUtils.IMEI_PATTERN.match(imei))
    
    @staticmethod
    def validate_id_card(id_card: str) -> bool:
        """验证身份证号格式"""
        return bool(GzApiUtils.ID_CARD_PATTERN.match(id_card))
    
    @staticmethod
    def print_colored(text: str, color: str = Fore.WHITE) -> None:
        """打印彩色文本"""
        print(f"{color}{text}{Style.RESET_ALL}")
    
    @staticmethod
    def print_success(text: str) -> None:
        """打印成功信息"""
        GzApiUtils.print_colored(f"✅ {text}", Fore.GREEN)
    
    @staticmethod
    def print_error(text: str) -> None:
        """打印错误信息"""
        GzApiUtils.print_colored(f"❌ {text}", Fore.RED)
    
    @staticmethod
    def print_warning(text: str) -> None:
        """打印警告信息"""
        GzApiUtils.print_colored(f"⚠️  {text}", Fore.YELLOW)
    
    @staticmethod
    def print_info(text: str) -> None:
        """打印信息"""
        GzApiUtils.print_colored(f"📋 {text}", Fore.CYAN)
    
    @staticmethod
    def print_section(title: str) -> None:
        """打印章节标题"""
        print(f"\n{Fore.BLUE}=== {title} ==={Style.RESET_ALL}")
    
    @staticmethod
    def get_input_with_default(prompt: str, default_value: str) -> str:
        """获取用户输入，支持默认值"""
        user_input = input(f"{prompt} [默认: {default_value}]: ").strip()
        return user_input if user_input else default_value
    
    @staticmethod
    def confirm_action(message: str) -> bool:
        """确认操作"""
        response = input(f"{message} (y/n): ").strip().lower()
        return response in ['y', 'yes']
    
    @staticmethod
    def parse_imei_list(imei_input: str) -> List[str]:
        """解析IMEI列表输入"""
        imei_list = [imei.strip() for imei in imei_input.split(',')]
        valid_imeis = []
        
        for imei in imei_list:
            if GzApiUtils.validate_imei(imei):
                valid_imeis.append(imei)
                GzApiUtils.print_success(f"{imei} - 格式正确")
            else:
                GzApiUtils.print_error(f"{imei} - 格式错误（需要15位数字）")
        
        return valid_imeis

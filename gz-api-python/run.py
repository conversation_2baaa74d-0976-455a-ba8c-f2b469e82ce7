#!/usr/bin/env python3
"""
快速启动脚本
支持直接运行特定功能或启动交互式菜单
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from gz_api.early_settlement_order import early_settlement_order_basic, early_settlement_order_manual
from gz_api.all_running_order_query import all_running_order_query_basic, all_running_order_query_manual
from gz_api.query_valid_imei import query_valid_imei_basic, query_valid_imei_manual
from gz_api.imei_code_push import imei_code_push_basic, imei_code_push_manual
from main import main

def show_usage():
    """显示使用说明"""
    print("🚀 公证接口Python实现 - 快速启动")
    print("="*50)
    print("使用方法:")
    print("  python run.py [功能名称]")
    print()
    print("可用功能:")
    print("  settlement          - 结清公证订单 (基础版本)")
    print("  settlement-manual   - 结清公证订单 (手动输入版本)")
    print("  order-query         - 用户有效订单查询 (基础版本)")
    print("  order-query-manual  - 用户有效订单查询 (手动输入版本)")
    print("  imei-query          - 查询有效的IMEI编码 (基础版本)")
    print("  imei-query-manual   - 查询有效的IMEI编码 (手动输入版本)")
    print("  imei-push           - 推送设备IMEI编码 (基础版本)")
    print("  imei-push-manual    - 推送设备IMEI编码 (手动输入版本)")
    print("  menu                - 启动交互式菜单")
    print()
    print("示例:")
    print("  python run.py settlement-manual")
    print("  python run.py imei-query")
    print("  python run.py menu")

def main_run():
    """主运行函数"""
    if len(sys.argv) < 2:
        show_usage()
        return
    
    command = sys.argv[1].lower()
    
    try:
        if command == 'settlement':
            early_settlement_order_basic()
        elif command == 'settlement-manual':
            early_settlement_order_manual()
        elif command == 'order-query':
            all_running_order_query_basic()
        elif command == 'order-query-manual':
            all_running_order_query_manual()
        elif command == 'imei-query':
            query_valid_imei_basic()
        elif command == 'imei-query-manual':
            query_valid_imei_manual()
        elif command == 'imei-push':
            imei_code_push_basic()
        elif command == 'imei-push-manual':
            imei_code_push_manual()
        elif command == 'menu':
            main()
        elif command in ['help', '-h', '--help']:
            show_usage()
        else:
            print(f"❌ 未知命令: {command}")
            print()
            show_usage()
    except KeyboardInterrupt:
        print("\n\n👋 程序已退出")
    except Exception as e:
        print(f"❌ 程序执行出错: {str(e)}")

if __name__ == "__main__":
    main_run()

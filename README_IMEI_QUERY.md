# 查询有效的IMEI编码功能实现

## 功能概述
根据公证接口v1.8文档中的"1.11 查询有效的imei编码"要求，实现了IMEI编码有效性查询功能。

## 实现文件

### 1. queryValidImei.java
- **功能**: 基础版本的IMEI查询功能
- **特点**: 使用固定参数进行测试
- **位置**: `gz-api-demo/src/main/java/com/gz/queryValidImei.java`

### 2. queryValidImeiManual.java  
- **功能**: 手动输入版本的IMEI查询功能
- **特点**: 支持控制台交互式输入参数
- **位置**: `gz-api-demo/src/main/java/com/gz/queryValidImeiManual.java`

## 配置信息

### 商户信息
- **商户号**: NJSJ1738988721355510
- **密钥**: ygAlUjvUnvq2KrYCuxzT5616C6lBksxk

### 接口信息
- **接口地址**: http://buss.haochengda.net:8088/gzs/queryValidImei
- **请求方式**: POST
- **内容类型**: application/json

## 请求参数

```json
{
    "gzOrderId": "公证订单ID",
    "imeiCode": "IMEI编码（15位数字）",
    "deviceType": "设备类型",
    "signature": "MD5签名"
}
```

## 使用方法

### 环境要求
1. Java 8 或更高版本
2. Maven 3.x（用于依赖管理）
3. 网络连接（访问公证接口服务器）

### 编译项目
```bash
cd gz-api-demo
mvn compile
```

### 运行基础版本
```bash
mvn exec:java -Dexec.mainClass="com.gz.queryValidImei"
```

### 运行手动输入版本
```bash
mvn exec:java -Dexec.mainClass="com.gz.queryValidImeiManual"
```

## 功能特性

### 基础版本特性
- 使用预设的测试参数
- 自动生成MD5签名
- 输出详细的请求和响应信息
- 包含异常处理

### 手动输入版本特性
- 交互式参数输入
- IMEI格式验证（15位数字）
- 支持默认值
- 请求确认机制
- 详细的错误提示

## 测试用例

### 默认测试参数
- **公证订单ID**: jwtest007
- **IMEI编码**: 123456789012345
- **设备类型**: 手机

### IMEI格式要求
- 必须是15位数字
- 不能包含字母或特殊字符
- 示例: 123456789012345

## 代码结构

### 签名生成
使用现有的 `Util.addDigest()` 方法生成MD5签名，确保与其他接口保持一致。

### HTTP请求
使用Hutool的HttpRequest发送POST请求，包含必要的请求头：
- GZ-Merchant-No: 商户号
- GZ-Req-Timestamp: 时间戳
- Content-Type: application/json

### 响应处理
- 输出原始响应数据
- 基础的响应格式检测
- 异常情况处理

## 注意事项

1. **网络连接**: 确保能够访问 http://buss.haochengda.net:8088
2. **参数验证**: IMEI编码必须符合15位数字格式
3. **签名安全**: 密钥信息已硬编码，生产环境建议使用配置文件
4. **错误处理**: 包含网络异常和参数错误的处理逻辑

## 扩展建议

1. **配置文件**: 将商户号和密钥移至配置文件
2. **日志系统**: 集成日志框架记录详细操作日志
3. **响应解析**: 根据实际响应格式完善解析逻辑
4. **批量查询**: 支持批量IMEI编码查询
5. **结果缓存**: 对查询结果进行缓存优化

## 故障排除

### 常见问题
1. **编译失败**: 检查Java版本和Maven配置
2. **网络超时**: 检查网络连接和防火墙设置
3. **签名错误**: 验证商户号和密钥是否正确
4. **参数格式错误**: 确保IMEI编码为15位数字

### 调试建议
1. 查看控制台输出的请求参数和签名
2. 检查网络连接状态
3. 验证接口地址是否正确
4. 确认商户号和密钥的有效性

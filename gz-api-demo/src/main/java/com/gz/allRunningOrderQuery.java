package com.gz;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson2.JSON;

import java.util.Map;
import java.util.TreeMap;

/**
 * 1.10 用户有效订单查询
 * 查询用户的有效公证订单数和在租台数
 */
public class allRunningOrderQuery {
    public static void main(String[] args) {
        // 使用提供的商户号和密钥
        String merchantNo = "NJSJ1738988721355510";
        String key = "ygAlUjvUnvq2KrYCuxzT5616C6lBksxk";
        
        // 构建请求参数
        Map<String, Object> paramMap = new TreeMap<>();
        paramMap.put("applicantID", "350521199812164511");  // 申请公证身份证号
        
        // 生成签名
        String md5Hex1 = Util.addDigest(paramMap, key);
        paramMap.put("signature", md5Hex1);
        
        System.out.println("=== 用户有效订单查询 ===");
        System.out.println("商户号: " + merchantNo);
        System.out.println("请求参数: " + JSON.toJSONString(paramMap));
        
        try {
            // 发送HTTP请求
            String result = HttpRequest.post("http://buss.haochengda.net:8088/gzs/allRunningOrderQuery")
                    .header("GZ-Merchant-No", merchantNo)
                    .header("GZ-Req-Timestamp", String.valueOf(System.currentTimeMillis()))
                    .header("Content-Type", "application/json")
                    .body(JSON.toJSONString(paramMap))
                    .execute().body();
            
            System.out.println("响应数据: " + result);
            
            // 解析响应结果
            parseResponse(result);
            
        } catch (Exception e) {
            System.err.println("请求失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 解析响应结果
     * @param response 响应JSON字符串
     */
    private static void parseResponse(String response) {
        try {
            System.out.println("\n=== 响应解析 ===");
            if (response != null && !response.isEmpty()) {
                System.out.println("用户订单查询结果: " + response);
                
                // 根据接口文档，响应包含：
                // isCertified: 用户有效公证订单数
                // goodsNum: 用户在租台数（最多5台）
                System.out.println("\n预期响应字段:");
                System.out.println("- isCertified: 用户有效公证订单数");
                System.out.println("- goodsNum: 用户在租台数（最多5台设备）");
            } else {
                System.out.println("响应为空");
            }
        } catch (Exception e) {
            System.err.println("响应解析失败: " + e.getMessage());
        }
    }
}

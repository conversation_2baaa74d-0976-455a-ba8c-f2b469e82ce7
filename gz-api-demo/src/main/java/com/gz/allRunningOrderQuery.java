package com.gz;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson2.JSON;

import java.util.Map;
import java.util.TreeMap;

/**
 * 1.10 用户有效订单查询
 * 查询用户的有效公证订单数和在租台数
 */
public class allRunningOrderQuery {
    public static void main(String[] args) {
        // 使用提供的商户号和密钥
        String merchantNo = "NJSJ1738988721355510";
        String key = "ygAlUjvUnvq2KrYCuxzT5616C6lBksxk";
        
        // 构建请求参数
        Map<String, Object> paramMap = new TreeMap<>();
        paramMap.put("applicantID", "350521199812164511");  // 申请公证身份证号
        
        // 生成签名
        String md5Hex1 = Util.addDigest(paramMap, key);
        paramMap.put("signature", md5Hex1);
        
        System.out.println("=== 用户有效订单查询 ===");
        System.out.println("商户号: " + merchantNo);
        System.out.println("请求参数: " + JSON.toJSONString(paramMap));
        
        try {
            // 发送HTTP请求
            String result = HttpRequest.post("http://buss.haochengda.net:8088/gzs/allRunningOrderQuery")
                    .header("GZ-Merchant-No", merchantNo)
                    .header("GZ-Req-Timestamp", String.valueOf(System.currentTimeMillis()))
                    .header("Content-Type", "application/json")
                    .body(JSON.toJSONString(paramMap))
                    .execute().body();
            
            System.out.println("响应数据: " + result);
            
            // 解析响应结果
            parseResponse(result);
            
        } catch (Exception e) {
            System.err.println("请求失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 解析响应结果
     * @param response 响应JSON字符串
     */
    private static void parseResponse(String response) {
        try {
            System.out.println("\n=== 响应解析 ===");
            if (response != null && !response.isEmpty()) {
                System.out.println("原始响应数据: " + response);

                // 尝试解析JSON响应
                if (response.startsWith("{")) {
                    try {
                        com.alibaba.fastjson2.JSONObject jsonResponse = JSON.parseObject(response);

                        System.out.println("\n=== 格式化结果 ===");

                        // 解析基本信息
                        Integer code = jsonResponse.getInteger("code");
                        String msg = jsonResponse.getString("msg");
                        Boolean success = jsonResponse.getBoolean("success");

                        System.out.println("📋 接口调用状态:");
                        System.out.println("   状态码: " + code + (code == 0 ? " (成功)" : " (失败)"));
                        System.out.println("   消息: " + msg);
                        System.out.println("   成功标识: " + success);

                        // 解析业务数据
                        com.alibaba.fastjson2.JSONObject data = jsonResponse.getJSONObject("data");
                        if (data != null) {
                            Integer isCertified = data.getInteger("isCertified");
                            Integer goodsNum = data.getInteger("goodsNum");

                            System.out.println("\n📊 用户订单信息:");
                            System.out.println("   有效公证订单数: " + isCertified + " 个");
                            System.out.println("   在租台数: " + goodsNum + " 台");

                            // 业务状态分析
                            System.out.println("\n🔍 业务状态分析:");
                            if (goodsNum == 0) {
                                System.out.println("   ✅ 用户当前没有在租设备");
                                System.out.println("   ✅ 可以申请新的公证服务");
                            } else if (goodsNum < 5) {
                                System.out.println("   ⚠️  用户当前有 " + goodsNum + " 台在租设备");
                                System.out.println("   ✅ 还可以申请 " + (5 - goodsNum) + " 台设备的公证");
                            } else if (goodsNum == 5) {
                                System.out.println("   ❌ 用户已达到最大设备数量限制（5台）");
                                System.out.println("   ❌ 无法申请新的公证服务");
                            } else {
                                System.out.println("   ⚠️  异常：在租台数超过限制 (" + goodsNum + " > 5)");
                            }

                            if (isCertified > 0) {
                                System.out.println("   📝 用户有 " + isCertified + " 个有效的公证订单");
                            } else {
                                System.out.println("   📝 用户当前没有有效的公证订单");
                            }
                        }

                    } catch (Exception e) {
                        System.err.println("JSON解析失败: " + e.getMessage());
                        System.out.println("响应格式: 非标准JSON或解析错误");
                    }
                } else {
                    System.out.println("响应格式: 非JSON格式");
                }
            } else {
                System.out.println("❌ 响应为空");
            }
        } catch (Exception e) {
            System.err.println("响应解析失败: " + e.getMessage());
        }
    }
}

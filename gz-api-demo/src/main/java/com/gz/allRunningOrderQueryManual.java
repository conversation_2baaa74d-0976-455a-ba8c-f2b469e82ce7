package com.gz;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson2.JSON;

import java.util.Map;
import java.util.Scanner;
import java.util.TreeMap;
import java.util.regex.Pattern;

/**
 * 1.10 用户有效订单查询 - 手动输入版本
 * 支持通过控制台手动输入身份证号查询用户的有效公证订单数和在租台数
 */
public class allRunningOrderQueryManual {
    
    // 身份证号格式验证正则表达式（18位）
    private static final Pattern ID_CARD_PATTERN = Pattern.compile("^\\d{17}[\\dXx]$");
    
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        // 使用提供的商户号和密钥
        String merchantNo = "NJSJ1738988721355510";
        String key = "ygAlUjvUnvq2KrYCuxzT5616C6lBksxk";
        
        System.out.println("=== 用户有效订单查询 - 手动输入模式 ===");
        System.out.println("商户号: " + merchantNo);
        System.out.println("功能说明: 查询用户的有效公证订单数和在租台数");
        System.out.println("注意: 公证处支持每人最多5台设备");
        System.out.println();
        
        try {
            // 获取用户输入的身份证号
            String applicantID = getValidIdCardInput(scanner);
            
            // 构建请求参数
            Map<String, Object> paramMap = new TreeMap<>();
            paramMap.put("applicantID", applicantID);
            
            // 生成签名
            String md5Hex1 = Util.addDigest(paramMap, key);
            paramMap.put("signature", md5Hex1);
            
            System.out.println("\n=== 请求信息 ===");
            System.out.println("申请公证身份证号: " + applicantID);
            System.out.println("请求参数: " + JSON.toJSONString(paramMap));
            
            // 确认发送请求
            System.out.print("\n是否发送请求？(y/n): ");
            String confirm = scanner.nextLine().trim().toLowerCase();
            
            if ("y".equals(confirm) || "yes".equals(confirm)) {
                sendRequest(merchantNo, paramMap);
            } else {
                System.out.println("已取消请求发送");
            }
            
        } catch (Exception e) {
            System.err.println("程序执行失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            scanner.close();
        }
    }
    
    /**
     * 获取有效的身份证号输入
     */
    private static String getValidIdCardInput(Scanner scanner) {
        while (true) {
            System.out.print("请输入申请公证身份证号（18位） [默认: 350521199812164511]: ");
            String input = scanner.nextLine().trim();
            
            // 如果为空，使用默认值
            if (input.isEmpty()) {
                input = "350521199812164511";
            }
            
            // 验证身份证号格式
            if (ID_CARD_PATTERN.matcher(input).matches()) {
                return input;
            } else {
                System.out.println("错误：身份证号必须是18位数字（最后一位可以是X），请重新输入！");
            }
        }
    }
    
    /**
     * 发送HTTP请求
     */
    private static void sendRequest(String merchantNo, Map<String, Object> paramMap) {
        try {
            System.out.println("\n=== 发送请求 ===");
            
            String result = HttpRequest.post("http://buss.haochengda.net:8088/gzs/allRunningOrderQuery")
                    .header("GZ-Merchant-No", merchantNo)
                    .header("GZ-Req-Timestamp", String.valueOf(System.currentTimeMillis()))
                    .header("Content-Type", "application/json")
                    .body(JSON.toJSONString(paramMap))
                    .execute().body();
            
            System.out.println("响应数据: " + result);
            
            // 解析响应结果
            parseResponse(result);
            
        } catch (Exception e) {
            System.err.println("请求失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 解析响应结果
     */
    private static void parseResponse(String response) {
        try {
            System.out.println("\n=== 响应解析 ===");
            if (response != null && !response.isEmpty()) {
                System.out.println("原始响应数据: " + response);

                // 尝试解析JSON响应
                if (response.startsWith("{")) {
                    try {
                        com.alibaba.fastjson2.JSONObject jsonResponse = JSON.parseObject(response);

                        System.out.println("\n=== 格式化结果 ===");

                        // 解析基本信息
                        Integer code = jsonResponse.getInteger("code");
                        String msg = jsonResponse.getString("msg");
                        Boolean success = jsonResponse.getBoolean("success");

                        System.out.println("📋 接口调用状态:");
                        System.out.println("   状态码: " + code + (code == 0 ? " (成功)" : " (失败)"));
                        System.out.println("   消息: " + msg);
                        System.out.println("   成功标识: " + success);

                        // 解析业务数据
                        com.alibaba.fastjson2.JSONObject data = jsonResponse.getJSONObject("data");
                        if (data != null) {
                            Integer isCertified = data.getInteger("isCertified");
                            Integer goodsNum = data.getInteger("goodsNum");

                            System.out.println("\n📊 用户订单信息:");
                            System.out.println("   有效公证订单数: " + isCertified + " 个");
                            System.out.println("   在租台数: " + goodsNum + " 台");

                            // 业务状态分析
                            System.out.println("\n🔍 业务状态分析:");
                            if (goodsNum == 0) {
                                System.out.println("   ✅ 用户当前没有在租设备");
                                System.out.println("   ✅ 可以申请新的公证服务");
                            } else if (goodsNum < 5) {
                                System.out.println("   ⚠️  用户当前有 " + goodsNum + " 台在租设备");
                                System.out.println("   ✅ 还可以申请 " + (5 - goodsNum) + " 台设备的公证");
                            } else if (goodsNum == 5) {
                                System.out.println("   ❌ 用户已达到最大设备数量限制（5台）");
                                System.out.println("   ❌ 无法申请新的公证服务");
                            } else {
                                System.out.println("   ⚠️  异常：在租台数超过限制 (" + goodsNum + " > 5)");
                            }

                            if (isCertified > 0) {
                                System.out.println("   📝 用户有 " + isCertified + " 个有效的公证订单");
                            } else {
                                System.out.println("   📝 用户当前没有有效的公证订单");
                            }
                        }

                        System.out.println("\n📖 字段说明:");
                        System.out.println("   - isCertified: 用户有效公证订单数");
                        System.out.println("   - goodsNum: 用户在租台数（最多5台设备）");

                    } catch (Exception e) {
                        System.err.println("JSON解析失败: " + e.getMessage());
                        System.out.println("响应格式: 非标准JSON或解析错误");
                    }
                } else {
                    System.out.println("响应格式: 非JSON格式");
                }
            } else {
                System.out.println("❌ 响应为空");
            }
        } catch (Exception e) {
            System.err.println("响应解析失败: " + e.getMessage());
        }
    }
}

package com.gz;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson2.JSON;

import java.util.Map;
import java.util.Scanner;
import java.util.TreeMap;
import java.util.regex.Pattern;

/**
 * 1.10 用户有效订单查询 - 手动输入版本
 * 支持通过控制台手动输入身份证号查询用户的有效公证订单数和在租台数
 */
public class allRunningOrderQueryManual {
    
    // 身份证号格式验证正则表达式（18位）
    private static final Pattern ID_CARD_PATTERN = Pattern.compile("^\\d{17}[\\dXx]$");
    
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        // 使用提供的商户号和密钥
        String merchantNo = "NJSJ1738988721355510";
        String key = "ygAlUjvUnvq2KrYCuxzT5616C6lBksxk";
        
        System.out.println("=== 用户有效订单查询 - 手动输入模式 ===");
        System.out.println("商户号: " + merchantNo);
        System.out.println("功能说明: 查询用户的有效公证订单数和在租台数");
        System.out.println("注意: 公证处支持每人最多5台设备");
        System.out.println();
        
        try {
            // 获取用户输入的身份证号
            String applicantID = getValidIdCardInput(scanner);
            
            // 构建请求参数
            Map<String, Object> paramMap = new TreeMap<>();
            paramMap.put("applicantID", applicantID);
            
            // 生成签名
            String md5Hex1 = Util.addDigest(paramMap, key);
            paramMap.put("signature", md5Hex1);
            
            System.out.println("\n=== 请求信息 ===");
            System.out.println("申请公证身份证号: " + applicantID);
            System.out.println("请求参数: " + JSON.toJSONString(paramMap));
            
            // 确认发送请求
            System.out.print("\n是否发送请求？(y/n): ");
            String confirm = scanner.nextLine().trim().toLowerCase();
            
            if ("y".equals(confirm) || "yes".equals(confirm)) {
                sendRequest(merchantNo, paramMap);
            } else {
                System.out.println("已取消请求发送");
            }
            
        } catch (Exception e) {
            System.err.println("程序执行失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            scanner.close();
        }
    }
    
    /**
     * 获取有效的身份证号输入
     */
    private static String getValidIdCardInput(Scanner scanner) {
        while (true) {
            System.out.print("请输入申请公证身份证号（18位） [默认: 350521199812164511]: ");
            String input = scanner.nextLine().trim();
            
            // 如果为空，使用默认值
            if (input.isEmpty()) {
                input = "350521199812164511";
            }
            
            // 验证身份证号格式
            if (ID_CARD_PATTERN.matcher(input).matches()) {
                return input;
            } else {
                System.out.println("错误：身份证号必须是18位数字（最后一位可以是X），请重新输入！");
            }
        }
    }
    
    /**
     * 发送HTTP请求
     */
    private static void sendRequest(String merchantNo, Map<String, Object> paramMap) {
        try {
            System.out.println("\n=== 发送请求 ===");
            
            String result = HttpRequest.post("http://buss.haochengda.net:8088/gzs/allRunningOrderQuery")
                    .header("GZ-Merchant-No", merchantNo)
                    .header("GZ-Req-Timestamp", String.valueOf(System.currentTimeMillis()))
                    .header("Content-Type", "application/json")
                    .body(JSON.toJSONString(paramMap))
                    .execute().body();
            
            System.out.println("响应数据: " + result);
            
            // 解析响应结果
            parseResponse(result);
            
        } catch (Exception e) {
            System.err.println("请求失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 解析响应结果
     */
    private static void parseResponse(String response) {
        try {
            System.out.println("\n=== 响应解析 ===");
            if (response != null && !response.isEmpty()) {
                System.out.println("用户订单查询结果: " + response);
                
                System.out.println("\n响应字段说明:");
                System.out.println("- isCertified: 用户有效公证订单数（整型）");
                System.out.println("- goodsNum: 用户在租台数（整型，最多5台设备）");
                System.out.println("\n业务规则:");
                System.out.println("- 如果在租台数为5，则无法再做公证");
                System.out.println("- 每人最多支持5台设备的公证");
                
                // 尝试解析JSON响应
                if (response.startsWith("{")) {
                    System.out.println("\n响应格式: JSON");
                } else {
                    System.out.println("\n响应格式: 文本");
                }
            } else {
                System.out.println("响应为空");
            }
        } catch (Exception e) {
            System.err.println("响应解析失败: " + e.getMessage());
        }
    }
}

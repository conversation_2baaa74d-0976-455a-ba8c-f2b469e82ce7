package com.gz;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson2.JSON;

import java.util.Map;
import java.util.Scanner;
import java.util.TreeMap;
import java.util.regex.Pattern;

/**
 * 1.11 查询有效的IMEI编码 - 手动输入版本
 * 支持通过控制台手动输入参数进行IMEI编码有效性查询
 * 接口路径: gzs/allImeiCodeQuery
 * 参数: imeiCodes (字符串数组)
 */
public class queryValidImeiManual {

    // IMEI编码格式验证正则表达式（15位数字）
    private static final Pattern IMEI_PATTERN = Pattern.compile("^\\d{15}$");

    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);

        // 使用提供的商户号和密钥
        String merchantNo = "NJSJ1738988721355510";
        String key = "ygAlUjvUnvq2KrYCuxzT5616C6lBksxk";

        System.out.println("=== 查询有效的IMEI编码 - 手动输入模式 ===");
        System.out.println("商户号: " + merchantNo);
        System.out.println("功能说明: 批量查询IMEI编码是否重复，支持多个IMEI同时查询");
        System.out.println();

        try {
            // 获取用户输入的IMEI编码列表
            String[] imeiCodes = getImeiCodesInput(scanner);

            // 构建请求参数 - 使用正确的参数格式
            Map<String, Object> paramMap = new TreeMap<>();
            paramMap.put("imeiCodes", imeiCodes);
            
            // 生成签名
            String md5Hex1 = Util.addDigest(paramMap, key);
            paramMap.put("signature", md5Hex1);
            
            System.out.println("\n=== 请求信息 ===");
            System.out.println("查询的IMEI编码: " + String.join(", ", imeiCodes));
            System.out.println("请求参数: " + JSON.toJSONString(paramMap));

            // 确认发送请求
            System.out.print("\n是否发送请求？(y/n): ");
            String confirm = scanner.nextLine().trim().toLowerCase();

            if ("y".equals(confirm) || "yes".equals(confirm)) {
                sendRequest(merchantNo, paramMap, imeiCodes);
            } else {
                System.out.println("已取消请求发送");
            }
            
        } catch (Exception e) {
            System.err.println("程序执行失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            scanner.close();
        }
    }
    
    /**
     * 获取IMEI编码列表输入
     */
    private static String[] getImeiCodesInput(Scanner scanner) {
        System.out.println("请输入要查询的IMEI编码（支持多个，用逗号分隔）:");
        System.out.print("IMEI编码 [默认: 123456789012345,355011479595868]: ");
        String input = scanner.nextLine().trim();

        // 如果为空，使用默认值
        if (input.isEmpty()) {
            input = "123456789012345,355011479595868";
        }

        // 分割并验证IMEI编码
        String[] imeiArray = input.split(",");
        java.util.List<String> validImeis = new java.util.ArrayList<>();

        for (String imei : imeiArray) {
            String trimmedImei = imei.trim();
            if (IMEI_PATTERN.matcher(trimmedImei).matches()) {
                validImeis.add(trimmedImei);
                System.out.println("✅ " + trimmedImei + " - 格式正确");
            } else {
                System.out.println("❌ " + trimmedImei + " - 格式错误（需要15位数字）");
            }
        }

        if (validImeis.isEmpty()) {
            System.out.println("没有有效的IMEI编码，使用默认值");
            return new String[]{"123456789012345", "355011479595868"};
        }

        return validImeis.toArray(new String[0]);
    }

    /**
     * 获取用户输入，支持默认值
     */
    private static String getInput(Scanner scanner, String prompt, String defaultValue) {
        System.out.print(prompt + " [默认: " + defaultValue + "]: ");
        String input = scanner.nextLine().trim();
        return input.isEmpty() ? defaultValue : input;
    }
    
    /**
     * 发送HTTP请求
     */
    private static void sendRequest(String merchantNo, Map<String, Object> paramMap, String[] imeiCodes) {
        try {
            System.out.println("\n=== 发送请求 ===");

            String result = HttpRequest.post("http://buss.haochengda.net:8088/gzs/allImeiCodeQuery")
                    .header("GZ-Merchant-No", merchantNo)
                    .header("GZ-Req-Timestamp", String.valueOf(System.currentTimeMillis()))
                    .header("Content-Type", "application/json")
                    .body(JSON.toJSONString(paramMap))
                    .execute().body();

            System.out.println("响应数据: " + result);

            // 解析响应结果
            parseResponse(result, imeiCodes);

        } catch (Exception e) {
            System.err.println("请求失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 解析响应结果
     */
    private static void parseResponse(String response, String[] imeiCodes) {
        try {
            System.out.println("\n=== 响应解析 ===");
            if (response != null && !response.isEmpty()) {
                System.out.println("原始响应数据: " + response);

                // 检查是否为404错误
                if (response.contains("404") || response.contains("Not Found") || response.contains("Whitelabel Error Page")) {
                    System.out.println("\n❌ 接口暂时不可用 (404错误)");
                    System.out.println("\n🔍 可能原因:");
                    System.out.println("   1. IMEI查询接口尚未在服务器端实现");
                    System.out.println("   2. 接口路径可能不正确");
                    System.out.println("   3. 该功能可能需要特殊权限");

                    System.out.println("\n💡 建议:");
                    System.out.println("   1. 联系接口提供方确认IMEI查询接口状态");
                    System.out.println("   2. 确认正确的接口路径");
                    System.out.println("   3. 代码逻辑正确，接口修复后即可正常使用");

                    System.out.println("\n📋 当前状态:");
                    System.out.println("   - 用户有效订单查询: ✅ 正常工作");
                    System.out.println("   - IMEI编码查询: ❌ 接口不可用");

                    return;
                }

                // 尝试解析JSON响应
                if (response.startsWith("{")) {
                    try {
                        com.alibaba.fastjson2.JSONObject jsonResponse = JSON.parseObject(response);

                        System.out.println("\n=== 格式化结果 ===");

                        // 解析基本信息
                        Integer code = jsonResponse.getInteger("code");
                        String msg = jsonResponse.getString("msg");
                        Boolean success = jsonResponse.getBoolean("success");

                        System.out.println("📋 接口调用状态:");
                        System.out.println("   状态码: " + code + (code == 0 ? " (成功)" : " (失败)"));
                        System.out.println("   消息: " + msg);
                        System.out.println("   成功标识: " + success);

                        // 解析业务数据 - 根据新的接口文档
                        com.alibaba.fastjson2.JSONObject data = jsonResponse.getJSONObject("data");
                        if (data != null && data.containsKey("imeiCodes")) {
                            com.alibaba.fastjson2.JSONArray duplicateImeis = data.getJSONArray("imeiCodes");

                            System.out.println("\n📱 IMEI查询结果:");
                            System.out.println("   查询的IMEI数量: " + imeiCodes.length + " 个");

                            if (duplicateImeis != null && !duplicateImeis.isEmpty()) {
                                System.out.println("   重复的IMEI数量: " + duplicateImeis.size() + " 个");
                                System.out.println("   重复的IMEI编码: " + duplicateImeis.toJSONString());

                                System.out.println("\n🔍 验证结果:");
                                System.out.println("   ❌ 发现重复的IMEI编码");
                                System.out.println("   ❌ 这些IMEI编码不可用于申请公证");

                                // 显示可用的IMEI
                                System.out.println("\n✅ 可用的IMEI编码:");
                                for (String imei : imeiCodes) {
                                    boolean isDuplicate = false;
                                    for (int i = 0; i < duplicateImeis.size(); i++) {
                                        if (imei.equals(duplicateImeis.getString(i))) {
                                            isDuplicate = true;
                                            break;
                                        }
                                    }
                                    if (!isDuplicate) {
                                        System.out.println("   ✅ " + imei + " (可用)");
                                    } else {
                                        System.out.println("   ❌ " + imei + " (重复，不可用)");
                                    }
                                }
                            } else {
                                System.out.println("   重复的IMEI数量: 0 个");

                                System.out.println("\n🔍 验证结果:");
                                System.out.println("   ✅ 所有IMEI编码都可用");
                                System.out.println("   ✅ 可以继续申请公证");

                                System.out.println("\n✅ 可用的IMEI编码:");
                                for (String imei : imeiCodes) {
                                    System.out.println("   ✅ " + imei + " (可用)");
                                }
                            }
                        } else {
                            System.out.println("\n⚠️  响应格式可能不同，显示原始数据:");
                            if (data != null) {
                                for (String key : data.keySet()) {
                                    Object value = data.get(key);
                                    System.out.println("   " + key + ": " + value);
                                }
                            } else {
                                System.out.println("   响应中没有data字段");
                            }
                        }

                        System.out.println("\n📖 说明:");
                        System.out.println("   此接口用于验证IMEI编码的有效性");
                        System.out.println("   IMEI编码是设备的唯一标识符");

                    } catch (Exception e) {
                        System.err.println("JSON解析失败: " + e.getMessage());
                        System.out.println("响应格式: 非标准JSON或解析错误");
                    }
                } else {
                    System.out.println("响应格式: 非JSON格式");
                    System.out.println("响应内容: " + response);
                }
            } else {
                System.out.println("❌ 响应为空");
            }
        } catch (Exception e) {
            System.err.println("响应解析失败: " + e.getMessage());
        }
    }
}

package com.gz;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson2.JSON;

import java.util.Map;
import java.util.Scanner;
import java.util.TreeMap;
import java.util.regex.Pattern;

/**
 * 1.11 查询有效的IMEI编码 - 手动输入版本
 * 支持通过控制台手动输入参数进行IMEI编码有效性查询
 */
public class queryValidImeiManual {
    
    // IMEI编码格式验证正则表达式（15位数字）
    private static final Pattern IMEI_PATTERN = Pattern.compile("^\\d{15}$");
    
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        // 使用提供的商户号和密钥
        String merchantNo = "NJSJ1738988721355510";
        String key = "ygAlUjvUnvq2KrYCuxzT5616C6lBksxk";
        
        System.out.println("=== 查询有效的IMEI编码 - 手动输入模式 ===");
        System.out.println("商户号: " + merchantNo);
        System.out.println();
        
        try {
            // 获取用户输入
            String gzOrderId = getInput(scanner, "请输入公证订单ID", "jwtest007");
            String imeiCode = getValidImeiInput(scanner);
            String deviceType = getInput(scanner, "请输入设备类型（如：手机、平板等）", "手机");
            
            // 构建请求参数
            Map<String, Object> paramMap = new TreeMap<>();
            paramMap.put("gzOrderId", gzOrderId);
            paramMap.put("imeiCode", imeiCode);
            paramMap.put("deviceType", deviceType);
            
            // 生成签名
            String md5Hex1 = Util.addDigest(paramMap, key);
            paramMap.put("signature", md5Hex1);
            
            System.out.println("\n=== 请求信息 ===");
            System.out.println("请求参数: " + JSON.toJSONString(paramMap));
            
            // 确认发送请求
            System.out.print("\n是否发送请求？(y/n): ");
            String confirm = scanner.nextLine().trim().toLowerCase();
            
            if ("y".equals(confirm) || "yes".equals(confirm)) {
                sendRequest(merchantNo, paramMap);
            } else {
                System.out.println("已取消请求发送");
            }
            
        } catch (Exception e) {
            System.err.println("程序执行失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            scanner.close();
        }
    }
    
    /**
     * 获取用户输入，支持默认值
     */
    private static String getInput(Scanner scanner, String prompt, String defaultValue) {
        System.out.print(prompt + " [默认: " + defaultValue + "]: ");
        String input = scanner.nextLine().trim();
        return input.isEmpty() ? defaultValue : input;
    }
    
    /**
     * 获取有效的IMEI编码输入
     */
    private static String getValidImeiInput(Scanner scanner) {
        while (true) {
            System.out.print("请输入IMEI编码（15位数字） [默认: 123456789012345]: ");
            String input = scanner.nextLine().trim();
            
            // 如果为空，使用默认值
            if (input.isEmpty()) {
                input = "123456789012345";
            }
            
            // 验证IMEI格式
            if (IMEI_PATTERN.matcher(input).matches()) {
                return input;
            } else {
                System.out.println("错误：IMEI编码必须是15位数字，请重新输入！");
            }
        }
    }
    
    /**
     * 发送HTTP请求
     */
    private static void sendRequest(String merchantNo, Map<String, Object> paramMap) {
        try {
            System.out.println("\n=== 发送请求 ===");
            
            String result = HttpRequest.post("http://buss.haochengda.net:8088/gzs/queryValidImei")
                    .header("GZ-Merchant-No", merchantNo)
                    .header("GZ-Req-Timestamp", String.valueOf(System.currentTimeMillis()))
                    .header("Content-Type", "application/json")
                    .body(JSON.toJSONString(paramMap))
                    .execute().body();
            
            System.out.println("响应数据: " + result);
            
            // 解析响应结果
            parseResponse(result);
            
        } catch (Exception e) {
            System.err.println("请求失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 解析响应结果
     */
    private static void parseResponse(String response) {
        try {
            System.out.println("\n=== 响应解析 ===");
            if (response != null && !response.isEmpty()) {
                System.out.println("IMEI查询结果: " + response);
                
                // 尝试解析JSON响应（根据实际响应格式调整）
                if (response.startsWith("{")) {
                    System.out.println("响应格式: JSON");
                } else {
                    System.out.println("响应格式: 文本");
                }
            } else {
                System.out.println("响应为空");
            }
        } catch (Exception e) {
            System.err.println("响应解析失败: " + e.getMessage());
        }
    }
}

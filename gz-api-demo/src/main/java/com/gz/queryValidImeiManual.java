package com.gz;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson2.JSON;

import java.util.Map;
import java.util.Scanner;
import java.util.TreeMap;
import java.util.regex.Pattern;

/**
 * 1.11 查询有效的IMEI编码 - 手动输入版本
 * 支持通过控制台手动输入参数进行IMEI编码有效性查询
 */
public class queryValidImeiManual {
    
    // IMEI编码格式验证正则表达式（15位数字）
    private static final Pattern IMEI_PATTERN = Pattern.compile("^\\d{15}$");
    
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        // 使用提供的商户号和密钥
        String merchantNo = "NJSJ1738988721355510";
        String key = "ygAlUjvUnvq2KrYCuxzT5616C6lBksxk";
        
        System.out.println("=== 查询有效的IMEI编码 - 手动输入模式 ===");
        System.out.println("商户号: " + merchantNo);
        System.out.println();
        
        try {
            // 获取用户输入
            String gzOrderId = getInput(scanner, "请输入公证订单ID", "jwtest007");
            String imeiCode = getValidImeiInput(scanner);
            String deviceType = getInput(scanner, "请输入设备类型（如：手机、平板等）", "手机");
            
            // 构建请求参数
            Map<String, Object> paramMap = new TreeMap<>();
            paramMap.put("gzOrderId", gzOrderId);
            paramMap.put("imeiCode", imeiCode);
            paramMap.put("deviceType", deviceType);
            
            // 生成签名
            String md5Hex1 = Util.addDigest(paramMap, key);
            paramMap.put("signature", md5Hex1);
            
            System.out.println("\n=== 请求信息 ===");
            System.out.println("请求参数: " + JSON.toJSONString(paramMap));
            
            // 确认发送请求
            System.out.print("\n是否发送请求？(y/n): ");
            String confirm = scanner.nextLine().trim().toLowerCase();
            
            if ("y".equals(confirm) || "yes".equals(confirm)) {
                sendRequest(merchantNo, paramMap);
            } else {
                System.out.println("已取消请求发送");
            }
            
        } catch (Exception e) {
            System.err.println("程序执行失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            scanner.close();
        }
    }
    
    /**
     * 获取用户输入，支持默认值
     */
    private static String getInput(Scanner scanner, String prompt, String defaultValue) {
        System.out.print(prompt + " [默认: " + defaultValue + "]: ");
        String input = scanner.nextLine().trim();
        return input.isEmpty() ? defaultValue : input;
    }
    
    /**
     * 获取有效的IMEI编码输入
     */
    private static String getValidImeiInput(Scanner scanner) {
        while (true) {
            System.out.print("请输入IMEI编码（15位数字） [默认: 123456789012345]: ");
            String input = scanner.nextLine().trim();
            
            // 如果为空，使用默认值
            if (input.isEmpty()) {
                input = "123456789012345";
            }
            
            // 验证IMEI格式
            if (IMEI_PATTERN.matcher(input).matches()) {
                return input;
            } else {
                System.out.println("错误：IMEI编码必须是15位数字，请重新输入！");
            }
        }
    }
    
    /**
     * 发送HTTP请求
     */
    private static void sendRequest(String merchantNo, Map<String, Object> paramMap) {
        try {
            System.out.println("\n=== 发送请求 ===");
            
            String result = HttpRequest.post("http://buss.haochengda.net:8088/gzs/queryValidImei")
                    .header("GZ-Merchant-No", merchantNo)
                    .header("GZ-Req-Timestamp", String.valueOf(System.currentTimeMillis()))
                    .header("Content-Type", "application/json")
                    .body(JSON.toJSONString(paramMap))
                    .execute().body();
            
            System.out.println("响应数据: " + result);
            
            // 解析响应结果
            parseResponse(result);
            
        } catch (Exception e) {
            System.err.println("请求失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 解析响应结果
     */
    private static void parseResponse(String response) {
        try {
            System.out.println("\n=== 响应解析 ===");
            if (response != null && !response.isEmpty()) {
                System.out.println("原始响应数据: " + response);

                // 检查是否为404错误
                if (response.contains("404") || response.contains("Not Found") || response.contains("Whitelabel Error Page")) {
                    System.out.println("\n❌ 接口暂时不可用 (404错误)");
                    System.out.println("\n🔍 可能原因:");
                    System.out.println("   1. IMEI查询接口尚未在服务器端实现");
                    System.out.println("   2. 接口路径可能不正确");
                    System.out.println("   3. 该功能可能需要特殊权限");

                    System.out.println("\n💡 建议:");
                    System.out.println("   1. 联系接口提供方确认IMEI查询接口状态");
                    System.out.println("   2. 确认正确的接口路径");
                    System.out.println("   3. 代码逻辑正确，接口修复后即可正常使用");

                    System.out.println("\n📋 当前状态:");
                    System.out.println("   - 用户有效订单查询: ✅ 正常工作");
                    System.out.println("   - IMEI编码查询: ❌ 接口不可用");

                    return;
                }

                // 尝试解析JSON响应
                if (response.startsWith("{")) {
                    try {
                        com.alibaba.fastjson2.JSONObject jsonResponse = JSON.parseObject(response);

                        System.out.println("\n=== 格式化结果 ===");

                        // 解析基本信息
                        Integer code = jsonResponse.getInteger("code");
                        String msg = jsonResponse.getString("msg");
                        Boolean success = jsonResponse.getBoolean("success");

                        System.out.println("📋 接口调用状态:");
                        System.out.println("   状态码: " + code + (code == 0 ? " (成功)" : " (失败)"));
                        System.out.println("   消息: " + msg);
                        System.out.println("   成功标识: " + success);

                        // 解析业务数据
                        com.alibaba.fastjson2.JSONObject data = jsonResponse.getJSONObject("data");
                        if (data != null) {
                            System.out.println("\n📱 IMEI查询结果:");

                            // 根据实际响应结构解析数据
                            for (String key : data.keySet()) {
                                Object value = data.get(key);
                                System.out.println("   " + key + ": " + value);
                            }

                            // 如果有特定字段，进行特殊处理
                            if (data.containsKey("isValid")) {
                                Boolean isValid = data.getBoolean("isValid");
                                System.out.println("\n🔍 验证结果:");
                                if (isValid != null && isValid) {
                                    System.out.println("   ✅ IMEI编码有效");
                                } else {
                                    System.out.println("   ❌ IMEI编码无效");
                                }
                            }
                        } else {
                            System.out.println("\n⚠️  响应中没有data字段，可能是接口返回格式不同");

                            // 尝试直接解析其他可能的字段
                            System.out.println("\n📱 响应内容:");
                            for (String key : jsonResponse.keySet()) {
                                if (!"code".equals(key) && !"msg".equals(key) && !"success".equals(key)) {
                                    Object value = jsonResponse.get(key);
                                    System.out.println("   " + key + ": " + value);
                                }
                            }
                        }

                        System.out.println("\n📖 说明:");
                        System.out.println("   此接口用于验证IMEI编码的有效性");
                        System.out.println("   IMEI编码是设备的唯一标识符");

                    } catch (Exception e) {
                        System.err.println("JSON解析失败: " + e.getMessage());
                        System.out.println("响应格式: 非标准JSON或解析错误");
                    }
                } else {
                    System.out.println("响应格式: 非JSON格式");
                    System.out.println("响应内容: " + response);
                }
            } else {
                System.out.println("❌ 响应为空");
            }
        } catch (Exception e) {
            System.err.println("响应解析失败: " + e.getMessage());
        }
    }
}

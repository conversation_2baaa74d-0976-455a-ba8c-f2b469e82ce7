package com.gz;

import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;

import java.time.LocalDate;
import java.util.Map;
import java.util.TreeMap;

/**
 * 1-录入公证信息
 */
public class enterGzData {
    public static void main(String[] args) {
        String merchantNo = "test";
        String key = "test";
        Map<String, Object> paramMap = new TreeMap<>();
        paramMap.put("gzOrderId","jwtest007");
        paramMap.put("lendStarttime", LocalDate.now());
        paramMap.put("lendEndtime", LocalDate.now().plusYears(1));
        paramMap.put("lenderName","曹某莫");
        paramMap.put("applicantName","杨军伟");
        paramMap.put("applicantID","350521199812164511");
        paramMap.put("applicantPhone","18859583602");
        paramMap.put("applicantAddress","地址1");
        paramMap.put("amount",15000);
        paramMap.put("goodsNum",1);
        paramMap.put("platformName","下单平台的公司主体名");
        paramMap.put("merchantName","给商品的平台公司主体名");
        paramMap.put("coDebtor","无");
        paramMap.put("cbUrl","http://127.0.0.1:8080/......");
        String md5Hex1 = Util.addDigest(paramMap, key);
        paramMap.put("signature",md5Hex1);
//        String result = HttpRequest.post("http://127.0.0.1:8080/gzs/enterGzData")
        String result = HttpRequest.post("http://buss.haochengda.net:8088/gzs/enterGzData")
                .header("GZ-Merchant-No", merchantNo)
                .header("GZ-Req-Timestamp", String.valueOf(System.currentTimeMillis()))
                .body(JSON.toJSONString(paramMap))
                .execute().body();
        System.out.println("响应数据:"+result);
    }
}

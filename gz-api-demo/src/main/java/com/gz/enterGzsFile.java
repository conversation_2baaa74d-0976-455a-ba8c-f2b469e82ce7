package com.gz;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson2.JSON;

import java.util.Map;
import java.util.TreeMap;
/**
 * 2-录入公证附件
 */
public class enterGzsFile {
    public static void main(String[] args) {
        String merchantNo = "test";
        String key = "test";
        Map<String, Object> paramMap = new TreeMap<>();
        paramMap.put("gzOrderId","jwtest007");
        paramMap.put("userIdNo","350521199812164511");
        paramMap.put("attachmentType","pdf");
        paramMap.put("fileBusiFlag","contract");
        paramMap.put("attachmentName","身份证a");
        paramMap.put("url","http://9v4zcz.natappfree.cc/file/OI2222.pdf");
        String md5Hex1 = Util.addDigest(paramMap, key);
        paramMap.put("signature",md5Hex1);
//        String result = HttpRequest.post("http://127.0.0.1:8080/gzs/enterGzsFile")
        String result = HttpRequest.post("http://buss.haochengda.net:8088/gzs/enterGzsFile")
                .header("GZ-Merchant-No", merchantNo)
                .header("GZ-Req-Timestamp", String.valueOf(System.currentTimeMillis()))
                .body(JSON.toJSONString(paramMap))
                .execute().body();
        System.out.println("响应数据:"+result);
    }
}

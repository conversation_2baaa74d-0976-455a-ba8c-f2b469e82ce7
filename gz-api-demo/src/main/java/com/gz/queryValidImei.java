package com.gz;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson2.JSON;

import java.util.Map;
import java.util.TreeMap;

/**
 * 1.11 查询有效的IMEI编码
 * 根据公证接口v1.8文档实现IMEI编码有效性查询功能
 */
public class queryValidImei {
    public static void main(String[] args) {
        // 使用提供的商户号和密钥
        String merchantNo = "NJSJ1738988721355510";
        String key = "ygAlUjvUnvq2KrYCuxzT5616C6lBksxk";
        
        // 构建请求参数
        Map<String, Object> paramMap = new TreeMap<>();
        paramMap.put("gzOrderId", "jwtest007");           // 公证订单ID
        paramMap.put("imeiCode", "123456789012345");      // IMEI编码（15位数字）
        paramMap.put("deviceType", "手机");                // 设备类型
        
        // 生成签名
        String md5Hex1 = Util.addDigest(paramMap, key);
        paramMap.put("signature", md5Hex1);
        
        System.out.println("=== 查询有效的IMEI编码 ===");
        System.out.println("商户号: " + merchantNo);
        System.out.println("请求参数: " + JSON.toJSONString(paramMap));
        
        try {
            // 发送HTTP请求
            String result = HttpRequest.post("http://buss.haochengda.net:8088/gzs/queryValidImei")
                    .header("GZ-Merchant-No", merchantNo)
                    .header("GZ-Req-Timestamp", String.valueOf(System.currentTimeMillis()))
                    .header("Content-Type", "application/json")
                    .body(JSON.toJSONString(paramMap))
                    .execute().body();
            
            System.out.println("响应数据: " + result);
            
            // 解析响应结果
            parseResponse(result);
            
        } catch (Exception e) {
            System.err.println("请求失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 解析响应结果
     * @param response 响应JSON字符串
     */
    private static void parseResponse(String response) {
        try {
            System.out.println("\n=== 响应解析 ===");
            // 这里可以根据实际的响应格式进行解析
            // 由于不知道具体的响应格式，先简单输出
            if (response != null && !response.isEmpty()) {
                System.out.println("IMEI查询结果: " + response);
            } else {
                System.out.println("响应为空");
            }
        } catch (Exception e) {
            System.err.println("响应解析失败: " + e.getMessage());
        }
    }
}

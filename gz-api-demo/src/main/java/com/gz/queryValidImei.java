package com.gz;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson2.JSON;

import java.util.Map;
import java.util.TreeMap;

/**
 * 1.11 查询有效的IMEI编码
 * 根据公证接口v1.8文档实现IMEI编码有效性查询功能
 */
public class queryValidImei {
    public static void main(String[] args) {
        // 使用提供的商户号和密钥
        String merchantNo = "NJSJ1738988721355510";
        String key = "ygAlUjvUnvq2KrYCuxzT5616C6lBksxk";
        
        // 构建请求参数
        Map<String, Object> paramMap = new TreeMap<>();
        paramMap.put("gzOrderId", "jwtest007");           // 公证订单ID
        paramMap.put("imeiCode", "123456789012345");      // IMEI编码（15位数字）
        paramMap.put("deviceType", "手机");                // 设备类型
        
        // 生成签名
        String md5Hex1 = Util.addDigest(paramMap, key);
        paramMap.put("signature", md5Hex1);
        
        System.out.println("=== 查询有效的IMEI编码 ===");
        System.out.println("商户号: " + merchantNo);
        System.out.println("请求参数: " + JSON.toJSONString(paramMap));
        
        try {
            // 发送HTTP请求
            String result = HttpRequest.post("http://buss.haochengda.net:8088/gzs/queryValidImei")
                    .header("GZ-Merchant-No", merchantNo)
                    .header("GZ-Req-Timestamp", String.valueOf(System.currentTimeMillis()))
                    .header("Content-Type", "application/json")
                    .body(JSON.toJSONString(paramMap))
                    .execute().body();
            
            System.out.println("响应数据: " + result);
            
            // 解析响应结果
            parseResponse(result);
            
        } catch (Exception e) {
            System.err.println("请求失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 解析响应结果
     * @param response 响应JSON字符串
     */
    private static void parseResponse(String response) {
        try {
            System.out.println("\n=== 响应解析 ===");
            if (response != null && !response.isEmpty()) {
                System.out.println("原始响应数据: " + response);

                // 尝试解析JSON响应
                if (response.startsWith("{")) {
                    try {
                        com.alibaba.fastjson2.JSONObject jsonResponse = JSON.parseObject(response);

                        System.out.println("\n=== 格式化结果 ===");

                        // 解析基本信息
                        Integer code = jsonResponse.getInteger("code");
                        String msg = jsonResponse.getString("msg");
                        Boolean success = jsonResponse.getBoolean("success");

                        System.out.println("📋 接口调用状态:");
                        System.out.println("   状态码: " + code + (code == 0 ? " (成功)" : " (失败)"));
                        System.out.println("   消息: " + msg);
                        System.out.println("   成功标识: " + success);

                        // 解析业务数据
                        com.alibaba.fastjson2.JSONObject data = jsonResponse.getJSONObject("data");
                        if (data != null) {
                            System.out.println("\n📱 IMEI查询结果:");

                            // 根据实际响应结构解析数据
                            for (String key : data.keySet()) {
                                Object value = data.get(key);
                                System.out.println("   " + key + ": " + value);
                            }

                            // 如果有特定字段，进行特殊处理
                            if (data.containsKey("isValid")) {
                                Boolean isValid = data.getBoolean("isValid");
                                System.out.println("\n🔍 验证结果:");
                                if (isValid != null && isValid) {
                                    System.out.println("   ✅ IMEI编码有效");
                                } else {
                                    System.out.println("   ❌ IMEI编码无效");
                                }
                            }
                        } else {
                            System.out.println("\n⚠️  响应中没有data字段，可能是接口返回格式不同");

                            // 尝试直接解析其他可能的字段
                            System.out.println("\n📱 响应内容:");
                            for (String key : jsonResponse.keySet()) {
                                if (!"code".equals(key) && !"msg".equals(key) && !"success".equals(key)) {
                                    Object value = jsonResponse.get(key);
                                    System.out.println("   " + key + ": " + value);
                                }
                            }
                        }

                        System.out.println("\n📖 说明:");
                        System.out.println("   此接口用于验证IMEI编码的有效性");

                    } catch (Exception e) {
                        System.err.println("JSON解析失败: " + e.getMessage());
                        System.out.println("响应格式: 非标准JSON或解析错误");
                    }
                } else {
                    System.out.println("响应格式: 非JSON格式");
                    System.out.println("响应内容: " + response);
                }
            } else {
                System.out.println("❌ 响应为空");
            }
        } catch (Exception e) {
            System.err.println("响应解析失败: " + e.getMessage());
        }
    }
}

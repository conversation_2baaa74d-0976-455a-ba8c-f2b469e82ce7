package com.gz;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson2.JSON;

import java.util.Map;
import java.util.Scanner;
import java.util.TreeMap;

/**
 * 1.9 结清公证订单 - 手动输入版本
 * 支持通过控制台手动输入参数进行公证订单结清操作
 */
public class earlySettlementOrderManual {
    
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        // 使用提供的商户号和密钥
        String merchantNo = "NJSJ1738988721355510";
        String key = "ygAlUjvUnvq2KrYCuxzT5616C6lBksxk";
        
        System.out.println("=== 结清公证订单 - 手动输入模式 ===");
        System.out.println("商户号: " + merchantNo);
        System.out.println("功能说明: 结清指定的公证订单，操作不可逆");
        System.out.println("⚠️  注意: 结清后订单将无法再进行相关操作");
        System.out.println();
        
        try {
            // 获取用户输入
            String gzOrderId = getInput(scanner, "请输入要结清的订单号（三方流水号）", "OI1970412769182351360");
            
            // 构建请求参数
            Map<String, Object> paramMap = new TreeMap<>();
            paramMap.put("gzOrderId", gzOrderId);
            
            // 生成签名
            String md5Hex1 = Util.addDigest(paramMap, key);
            paramMap.put("signature", md5Hex1);
            
            System.out.println("\n=== 请求信息 ===");
            System.out.println("订单号: " + gzOrderId);
            System.out.println("请求参数: " + JSON.toJSONString(paramMap));
            
            // 二次确认
            System.out.println("\n⚠️  重要提醒:");
            System.out.println("   - 结清操作不可逆");
            System.out.println("   - 结清后订单将无法再进行相关操作");
            System.out.println("   - 请确认订单号正确");
            
            System.out.print("\n确认要结清订单 " + gzOrderId + " 吗？(y/n): ");
            String confirm = scanner.nextLine().trim().toLowerCase();
            
            if ("y".equals(confirm) || "yes".equals(confirm)) {
                System.out.print("请再次确认结清操作 (输入 'CONFIRM' 继续): ");
                String finalConfirm = scanner.nextLine().trim();
                
                if ("CONFIRM".equals(finalConfirm)) {
                    sendRequest(merchantNo, paramMap);
                } else {
                    System.out.println("❌ 确认失败，已取消结清操作");
                }
            } else {
                System.out.println("已取消结清操作");
            }
            
        } catch (Exception e) {
            System.err.println("程序执行失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            scanner.close();
        }
    }
    
    /**
     * 获取用户输入，支持默认值
     */
    private static String getInput(Scanner scanner, String prompt, String defaultValue) {
        System.out.print(prompt + " [默认: " + defaultValue + "]: ");
        String input = scanner.nextLine().trim();
        return input.isEmpty() ? defaultValue : input;
    }
    
    /**
     * 发送HTTP请求
     */
    private static void sendRequest(String merchantNo, Map<String, Object> paramMap) {
        try {
            System.out.println("\n=== 发送请求 ===");
            
            String result = HttpRequest.post("http://buss.haochengda.net:8088/gzs/earlySettlementOrder")
                    .header("GZ-Merchant-No", merchantNo)
                    .header("GZ-Req-Timestamp", String.valueOf(System.currentTimeMillis()))
                    .header("Content-Type", "application/json")
                    .body(JSON.toJSONString(paramMap))
                    .execute().body();
            
            System.out.println("响应数据: " + result);
            
            // 解析响应结果
            parseResponse(result);
            
        } catch (Exception e) {
            System.err.println("请求失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 解析响应结果
     */
    private static void parseResponse(String response) {
        try {
            System.out.println("\n=== 响应解析 ===");
            if (response != null && !response.isEmpty()) {
                System.out.println("原始响应数据: " + response);
                
                // 尝试解析JSON响应
                if (response.startsWith("{")) {
                    try {
                        com.alibaba.fastjson2.JSONObject jsonResponse = JSON.parseObject(response);
                        
                        System.out.println("\n=== 格式化结果 ===");
                        
                        // 解析基本信息
                        Integer code = jsonResponse.getInteger("code");
                        String msg = jsonResponse.getString("msg");
                        Boolean success = jsonResponse.getBoolean("success");
                        
                        System.out.println("📋 接口调用状态:");
                        System.out.println("   状态码: " + code + (code == 0 ? " (成功)" : " (失败)"));
                        System.out.println("   消息: " + msg);
                        System.out.println("   成功标识: " + success);
                        
                        // 根据响应状态显示结果
                        if (code != null && code == 0 && success != null && success) {
                            System.out.println("\n🎉 结清结果:");
                            System.out.println("   ✅ 公证订单结清成功");
                            System.out.println("   ✅ 订单状态已更新为结清状态");
                            
                            System.out.println("\n📝 说明:");
                            System.out.println("   - 订单结清后将无法再进行相关操作");
                            System.out.println("   - 结清操作不可逆，请确认订单信息正确");
                            System.out.println("   - 结清后相关设备和资源将被释放");
                            
                            System.out.println("\n🔄 后续操作:");
                            System.out.println("   - 可以查询订单状态确认结清结果");
                            System.out.println("   - 相关设备可以用于新的公证申请");
                        } else {
                            System.out.println("\n❌ 结清结果:");
                            System.out.println("   ❌ 公证订单结清失败");
                            System.out.println("   原因: " + msg);
                            
                            System.out.println("\n💡 可能原因:");
                            System.out.println("   - 订单号不存在");
                            System.out.println("   - 订单状态不允许结清");
                            System.out.println("   - 订单已经结清");
                            System.out.println("   - 权限不足");
                            
                            System.out.println("\n🔧 建议操作:");
                            System.out.println("   - 检查订单号是否正确");
                            System.out.println("   - 查询订单当前状态");
                            System.out.println("   - 联系技术支持确认订单状态");
                        }
                        
                    } catch (Exception e) {
                        System.err.println("JSON解析失败: " + e.getMessage());
                        System.out.println("响应格式: 非标准JSON或解析错误");
                    }
                } else {
                    System.out.println("响应格式: 非JSON格式");
                    System.out.println("响应内容: " + response);
                }
            } else {
                System.out.println("❌ 响应为空");
            }
        } catch (Exception e) {
            System.err.println("响应解析失败: " + e.getMessage());
        }
    }
}

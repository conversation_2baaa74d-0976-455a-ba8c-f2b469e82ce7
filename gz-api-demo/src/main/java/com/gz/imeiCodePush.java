package com.gz;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson2.JSON;

import java.util.Map;
import java.util.TreeMap;

/**
 * 1.7 推送设备IMEI编码
 * 当公证书状态是视频已通过时调用此接口推送设备IMEI编码
 */
public class imeiCodePush {
    public static void main(String[] args) {
        // 使用提供的商户号和密钥
        String merchantNo = "NJSJ1738988721355510";
        String key = "ygAlUjvUnvq2KrYCuxzT5616C6lBksxk";
        
        // 构建请求参数
        Map<String, Object> paramMap = new TreeMap<>();
        paramMap.put("gzOrderId", "OI1970412769182351360");    // 订单号（三方流水号，唯一）
        paramMap.put("imeiCode", "355011479595868");          // 设备IMEI编码
        paramMap.put("isSecondHand", "2");                    // 是否是二手机：1-是，2-否
        
        // 生成签名
        String md5Hex1 = Util.addDigest(paramMap, key);
        paramMap.put("signature", md5Hex1);
        
        System.out.println("=== 推送设备IMEI编码 ===");
        System.out.println("商户号: " + merchantNo);
        System.out.println("订单号: " + paramMap.get("gzOrderId"));
        System.out.println("IMEI编码: " + paramMap.get("imeiCode"));
        System.out.println("是否二手机: " + (paramMap.get("isSecondHand").equals("1") ? "是" : "否"));
        System.out.println("请求参数: " + JSON.toJSONString(paramMap));
        
        try {
            // 发送HTTP请求
            String result = HttpRequest.post("http://buss.haochengda.net:8088/gzs/imeiCodePush")
                    .header("GZ-Merchant-No", merchantNo)
                    .header("GZ-Req-Timestamp", String.valueOf(System.currentTimeMillis()))
                    .header("Content-Type", "application/json")
                    .body(JSON.toJSONString(paramMap))
                    .execute().body();
            
            System.out.println("响应数据: " + result);
            
            // 解析响应结果
            parseResponse(result);
            
        } catch (Exception e) {
            System.err.println("请求失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 解析响应结果
     * @param response 响应JSON字符串
     */
    private static void parseResponse(String response) {
        try {
            System.out.println("\n=== 响应解析 ===");
            if (response != null && !response.isEmpty()) {
                System.out.println("原始响应数据: " + response);
                
                // 尝试解析JSON响应
                if (response.startsWith("{")) {
                    try {
                        com.alibaba.fastjson2.JSONObject jsonResponse = JSON.parseObject(response);
                        
                        System.out.println("\n=== 格式化结果 ===");
                        
                        // 解析基本信息
                        Integer code = jsonResponse.getInteger("code");
                        String msg = jsonResponse.getString("msg");
                        Boolean success = jsonResponse.getBoolean("success");
                        
                        System.out.println("📋 接口调用状态:");
                        System.out.println("   状态码: " + code + (code == 0 ? " (成功)" : " (失败)"));
                        System.out.println("   消息: " + msg);
                        System.out.println("   成功标识: " + success);
                        
                        // 根据响应状态显示结果
                        if (code != null && code == 0 && success != null && success) {
                            System.out.println("\n🎉 推送结果:");
                            System.out.println("   ✅ IMEI编码推送成功");
                            System.out.println("   ✅ 设备信息已记录到公证系统");
                            
                            System.out.println("\n📝 说明:");
                            System.out.println("   - 此接口在公证书状态为'视频已通过'时调用");
                            System.out.println("   - 用于将设备IMEI编码推送到公证系统");
                            System.out.println("   - 推送成功后设备信息将被永久记录");
                        } else {
                            System.out.println("\n❌ 推送结果:");
                            System.out.println("   ❌ IMEI编码推送失败");
                            System.out.println("   原因: " + msg);
                        }
                        
                    } catch (Exception e) {
                        System.err.println("JSON解析失败: " + e.getMessage());
                        System.out.println("响应格式: 非标准JSON或解析错误");
                    }
                } else {
                    System.out.println("响应格式: 非JSON格式");
                    System.out.println("响应内容: " + response);
                }
            } else {
                System.out.println("❌ 响应为空");
            }
        } catch (Exception e) {
            System.err.println("响应解析失败: " + e.getMessage());
        }
    }
}

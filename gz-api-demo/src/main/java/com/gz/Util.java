package com.gz;

import cn.hutool.crypto.digest.DigestUtil;

import java.util.Map;

public class Util {
    public static String addDigest(Map<String, Object> paramMap,String key) {
        StringBuilder digest = new StringBuilder();

        paramMap.forEach((k, v) -> {
            digest.append(k);
            digest.append("=");
            digest.append(v);
            digest.append("&");
        });
        digest.append("key="+key);
        System.out.println("明文摘要："+digest);
        String md5Hex1 = DigestUtil.md5Hex(digest.toString()).toUpperCase();
        System.out.println("进行md5加密并大写的摘要:"+md5Hex1);
        return md5Hex1;
    }
}

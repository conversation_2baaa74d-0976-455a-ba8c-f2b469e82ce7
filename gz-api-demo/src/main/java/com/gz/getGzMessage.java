package com.gz;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson2.JSON;

import java.util.Map;
import java.util.TreeMap;
/**
 * 5.1.7　获取公证书原件和人员信息当前状态
 */
public class getGzMessage {
    public static void main(String[] args) {
        String merchantNo = "test";
        String key = "test";
        Map<String, Object> paramMap = new TreeMap<>();
        paramMap.put("gzOrderId","jwtest007");
        String md5Hex1 = Util.addDigest(paramMap, key);
        paramMap.put("signature",md5Hex1);
//        String result = HttpRequest.post("http://127.0.0.1:8080/gzs/getGzsMessage")
        String result = HttpRequest.post("http://buss.haochengda.net:8088/gzs/getGzsMessage")
                .header("GZ-Merchant-No", merchantNo)
                .header("GZ-Req-Timestamp", String.valueOf(System.currentTimeMillis()))
                .body(JSON.toJSONString(paramMap))
                .execute().body();
        System.out.println("响应数据:"+result);
    }
}

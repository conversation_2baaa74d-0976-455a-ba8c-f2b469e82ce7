package com.gz;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson2.JSON;

import java.util.Map;
import java.util.Scanner;
import java.util.TreeMap;
import java.util.regex.Pattern;

/**
 * 1.7 推送设备IMEI编码 - 手动输入版本
 * 支持通过控制台手动输入参数进行设备IMEI编码推送
 */
public class imeiCodePushManual {
    
    // IMEI编码格式验证正则表达式（15位数字）
    private static final Pattern IMEI_PATTERN = Pattern.compile("^\\d{15}$");
    
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        // 使用提供的商户号和密钥
        String merchantNo = "NJSJ1738988721355510";
        String key = "ygAlUjvUnvq2KrYCuxzT5616C6lBksxk";
        
        System.out.println("=== 推送设备IMEI编码 - 手动输入模式 ===");
        System.out.println("商户号: " + merchantNo);
        System.out.println("功能说明: 当公证书状态是视频已通过时推送设备IMEI编码");
        System.out.println();
        
        try {
            // 获取用户输入
            String gzOrderId = getInput(scanner, "请输入订单号（三方流水号）", "OI1970412769182351360");
            String imeiCode = getValidImeiInput(scanner);
            String isSecondHand = getSecondHandInput(scanner);
            
            // 构建请求参数
            Map<String, Object> paramMap = new TreeMap<>();
            paramMap.put("gzOrderId", gzOrderId);
            paramMap.put("imeiCode", imeiCode);
            paramMap.put("isSecondHand", isSecondHand);
            
            // 生成签名
            String md5Hex1 = Util.addDigest(paramMap, key);
            paramMap.put("signature", md5Hex1);
            
            System.out.println("\n=== 请求信息 ===");
            System.out.println("订单号: " + gzOrderId);
            System.out.println("IMEI编码: " + imeiCode);
            System.out.println("是否二手机: " + (isSecondHand.equals("1") ? "是" : "否"));
            System.out.println("请求参数: " + JSON.toJSONString(paramMap));
            
            // 确认发送请求
            System.out.print("\n是否发送请求？(y/n): ");
            String confirm = scanner.nextLine().trim().toLowerCase();
            
            if ("y".equals(confirm) || "yes".equals(confirm)) {
                sendRequest(merchantNo, paramMap);
            } else {
                System.out.println("已取消请求发送");
            }
            
        } catch (Exception e) {
            System.err.println("程序执行失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            scanner.close();
        }
    }
    
    /**
     * 获取用户输入，支持默认值
     */
    private static String getInput(Scanner scanner, String prompt, String defaultValue) {
        System.out.print(prompt + " [默认: " + defaultValue + "]: ");
        String input = scanner.nextLine().trim();
        return input.isEmpty() ? defaultValue : input;
    }
    
    /**
     * 获取有效的IMEI编码输入
     */
    private static String getValidImeiInput(Scanner scanner) {
        while (true) {
            System.out.print("请输入IMEI编码（15位数字） [默认: 355011479595868]: ");
            String input = scanner.nextLine().trim();
            
            // 如果为空，使用默认值
            if (input.isEmpty()) {
                input = "355011479595868";
            }
            
            // 验证IMEI格式
            if (IMEI_PATTERN.matcher(input).matches()) {
                return input;
            } else {
                System.out.println("错误：IMEI编码必须是15位数字，请重新输入！");
            }
        }
    }
    
    /**
     * 获取是否二手机输入
     */
    private static String getSecondHandInput(Scanner scanner) {
        while (true) {
            System.out.print("是否是二手机？(1-是, 2-否) [默认: 2]: ");
            String input = scanner.nextLine().trim();
            
            // 如果为空，使用默认值
            if (input.isEmpty()) {
                input = "2";
            }
            
            // 验证输入
            if ("1".equals(input) || "2".equals(input)) {
                return input;
            } else {
                System.out.println("错误：请输入1（是）或2（否），请重新输入！");
            }
        }
    }
    
    /**
     * 发送HTTP请求
     */
    private static void sendRequest(String merchantNo, Map<String, Object> paramMap) {
        try {
            System.out.println("\n=== 发送请求 ===");
            
            String result = HttpRequest.post("http://buss.haochengda.net:8088/gzs/imeiCodePush")
                    .header("GZ-Merchant-No", merchantNo)
                    .header("GZ-Req-Timestamp", String.valueOf(System.currentTimeMillis()))
                    .header("Content-Type", "application/json")
                    .body(JSON.toJSONString(paramMap))
                    .execute().body();
            
            System.out.println("响应数据: " + result);
            
            // 解析响应结果
            parseResponse(result);
            
        } catch (Exception e) {
            System.err.println("请求失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 解析响应结果
     */
    private static void parseResponse(String response) {
        try {
            System.out.println("\n=== 响应解析 ===");
            if (response != null && !response.isEmpty()) {
                System.out.println("原始响应数据: " + response);
                
                // 尝试解析JSON响应
                if (response.startsWith("{")) {
                    try {
                        com.alibaba.fastjson2.JSONObject jsonResponse = JSON.parseObject(response);
                        
                        System.out.println("\n=== 格式化结果 ===");
                        
                        // 解析基本信息
                        Integer code = jsonResponse.getInteger("code");
                        String msg = jsonResponse.getString("msg");
                        Boolean success = jsonResponse.getBoolean("success");
                        
                        System.out.println("📋 接口调用状态:");
                        System.out.println("   状态码: " + code + (code == 0 ? " (成功)" : " (失败)"));
                        System.out.println("   消息: " + msg);
                        System.out.println("   成功标识: " + success);
                        
                        // 根据响应状态显示结果
                        if (code != null && code == 0 && success != null && success) {
                            System.out.println("\n🎉 推送结果:");
                            System.out.println("   ✅ IMEI编码推送成功");
                            System.out.println("   ✅ 设备信息已记录到公证系统");
                            
                            System.out.println("\n📝 说明:");
                            System.out.println("   - 此接口在公证书状态为'视频已通过'时调用");
                            System.out.println("   - 用于将设备IMEI编码推送到公证系统");
                            System.out.println("   - 推送成功后设备信息将被永久记录");
                        } else {
                            System.out.println("\n❌ 推送结果:");
                            System.out.println("   ❌ IMEI编码推送失败");
                            System.out.println("   原因: " + msg);
                        }
                        
                    } catch (Exception e) {
                        System.err.println("JSON解析失败: " + e.getMessage());
                        System.out.println("响应格式: 非标准JSON或解析错误");
                    }
                } else {
                    System.out.println("响应格式: 非JSON格式");
                    System.out.println("响应内容: " + response);
                }
            } else {
                System.out.println("❌ 响应为空");
            }
        } catch (Exception e) {
            System.err.println("响应解析失败: " + e.getMessage());
        }
    }
}

# VS Code Java 开发环境设置指南

## 环境配置完成 ✅

你的Mac系统已经成功配置了Java开发环境：

- Java 17 已安装并配置
- Maven 3.9.11 已安装
- VS Code配置文件已创建

## VS Code扩展安装

请在VS Code中安装以下扩展：

1. **Extension Pack for Java** (Microsoft)
   - 包含了所有Java开发必需的扩展包

2. **Maven for Java** (Microsoft)
   - 用于Maven项目管理

3. **Debugger for Java** (Microsoft)
   - Java调试工具

4. **Test Runner for Java** (Microsoft)
   - Java测试运行器

安装方法：
1. 打开VS Code
2. 按 `Cmd+Shift+X` 打开扩展面板
3. 搜索上述扩展名称并安装

## 项目运行步骤

### 方法一：使用VS Code运行（推荐）

1. **打开项目**
   ```
   code /Users/<USER>/Desktop/Project\ Workspace/GZ/gz-api-demo
   ```

2. **等待VS Code加载项目**
   - VS Code会自动检测Maven项目并下载依赖
   - 右下角会显示Java项目加载进度

3. **运行程序**
   - 打开 `src/main/java/com/gz/queryValidImei.java` 或 `queryValidImeiManual.java`
   - 点击代码上方的 `Run` 或 `Debug` 按钮
   - 或者按 `F5` 启动调试

### 方法二：使用命令行运行

1. **进入项目目录**
   ```bash
   cd /Users/<USER>/Desktop/Project\ Workspace/GZ/gz-api-demo
   ```

2. **编译项目**
   ```bash
   mvn compile
   ```

3. **运行基础版本**
   ```bash
   mvn exec:java -Dexec.mainClass="com.gz.queryValidImei"
   ```

4. **运行手动输入版本**
   ```bash
   mvn exec:java -Dexec.mainClass="com.gz.queryValidImeiManual"
   ```

## 项目说明

### 文件结构
```
gz-api-demo/
├── pom.xml                 # Maven项目配置
├── src/
│   ├── main/java/com/gz/
│   │   ├── queryValidImei.java        # 基础版本
│   │   ├── queryValidImeiManual.java  # 手动输入版本
│   │   └── Util.java                  # 工具类
│   └── test/java/com/gz/
│       └── AppTest.java
└── .vscode/               # VS Code配置
    ├── settings.json
    ├── launch.json
    └── java.properties.json
```

### 功能说明

1. **queryValidImei.java** - 基础版本
   - 使用固定参数测试
   - 适合快速验证接口功能

2. **queryValidImeiManual.java** - 手动输入版本
   - 支持交互式参数输入
   - IMEI格式验证（15位数字）
   - 用户友好的操作界面

### 常见问题

1. **VS Code无法识别Java项目**
   - 确保已安装Java扩展包
   - 重新加载VS Code窗口（`Cmd+Shift+P` → `Developer: Reload Window`）

2. **Maven依赖下载失败**
   - 检查网络连接
   - 尝试使用阿里云镜像源（在pom.xml中添加）

3. **Java版本不匹配**
   - 确保使用Java 17
   - 在VS Code设置中指定Java路径

## 下一步

现在你可以：
1. 使用VS Code打开项目
2. 运行 `queryValidImei.java` 进行基础测试
3. 运行 `queryValidImeiManual.java` 进行交互式测试

如果遇到任何问题，请检查VS Code的Java扩展是否正确安装并加载。
{"java.configuration.updateBuildConfiguration": "automatic", "java.compile.nullAnalysis.mode": "automatic", "java.format.settings.url": ".vscode/java-formatter.xml", "java.format.settings.profile": "GoogleStyle", "maven.executable.path": "/opt/homebrew/bin/mvn", "java.home": "/opt/homebrew/opt/openjdk@17", "java.jdt.ls.java.home": "/opt/homebrew/opt/openjdk@17", "java.configuration.maven.userSettings": "/opt/homebrew/Cellar/maven/3.9.11/libexec/conf/settings.xml", "terminal.integrated.env.osx": {"PATH": "/opt/homebrew/opt/openjdk@17/bin:${env:PATH}"}}
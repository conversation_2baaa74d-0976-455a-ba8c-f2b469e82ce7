# 公证接口功能演示示例

## 🎯 已实现功能概览

现在项目中包含以下公证接口功能：

### 1.10 用户有效订单查询
- **功能**: 查询用户的有效公证订单数和在租台数
- **业务规则**: 每人最多5台设备，在租台数为5时无法再做公证
- **输入**: 身份证号（18位）
- **输出**: 有效订单数、在租台数

### 1.11 查询有效的IMEI编码
- **功能**: 验证IMEI编码的有效性
- **输入**: 公证订单ID、IMEI编码（15位）、设备类型
- **输出**: IMEI有效性验证结果

## 🚀 快速演示

### 演示1：用户有效订单查询

#### 使用基础版本（固定参数）
```bash
# 运行基础版本，使用默认身份证号
java -cp "target/classes:lib/*" com.gz.allRunningOrderQuery
```

**预期输出**：
```
=== 用户有效订单查询 ===
商户号: NJSJ1738988721355510
请求参数: {"applicantID":"350521199812164511","signature":"..."}
响应数据: {...}
```

#### 使用手动输入版本（交互式）
```bash
# 运行手动输入版本
java -cp "target/classes:lib/*" com.gz.allRunningOrderQueryManual
```

**交互示例**：
```
=== 用户有效订单查询 - 手动输入模式 ===
商户号: NJSJ1738988721355510
功能说明: 查询用户的有效公证订单数和在租台数
注意: 公证处支持每人最多5台设备

请输入申请公证身份证号（18位） [默认: 350521199812164511]: 
[直接按回车使用默认值，或输入新的身份证号]

=== 请求信息 ===
申请公证身份证号: 350521199812164511
请求参数: {"applicantID":"350521199812164511","signature":"..."}

是否发送请求？(y/n): y
```

### 演示2：IMEI编码查询

#### 使用基础版本（固定参数）
```bash
# 运行基础版本，使用默认参数
java -cp "target/classes:lib/*" com.gz.queryValidImei
```

**预期输出**：
```
=== 查询有效的IMEI编码 ===
商户号: NJSJ1738988721355510
请求参数: {"deviceType":"手机","gzOrderId":"jwtest007","imeiCode":"123456789012345","signature":"..."}
响应数据: {...}
```

#### 使用手动输入版本（交互式）
```bash
# 运行手动输入版本
java -cp "target/classes:lib/*" com.gz.queryValidImeiManual
```

**交互示例**：
```
=== 查询有效的IMEI编码 - 手动输入模式 ===
商户号: NJSJ1738988721355510

请输入公证订单ID [默认: jwtest007]: 
请输入IMEI编码（15位数字） [默认: 123456789012345]: 
请输入设备类型（如：手机、平板等） [默认: 手机]: 

=== 请求信息 ===
请求参数: {"deviceType":"手机","gzOrderId":"jwtest007","imeiCode":"123456789012345","signature":"..."}

是否发送请求？(y/n): y
```

## 📋 测试场景

### 场景1：正常业务流程
1. **查询用户订单状态**
   - 输入身份证号：350521199812164511
   - 查看用户当前有效订单数和在租台数
   - 确认是否可以继续申请公证

2. **验证设备IMEI**
   - 输入公证订单ID：jwtest007
   - 输入设备IMEI：123456789012345
   - 验证设备编码有效性

### 场景2：边界条件测试
1. **测试在租台数限制**
   - 查询在租台数为5的用户
   - 验证系统是否正确提示无法再做公证

2. **测试无效输入**
   - 输入错误格式的身份证号
   - 输入错误格式的IMEI编码
   - 观察系统验证和错误提示

### 场景3：错误处理测试
1. **网络异常**
   - 断开网络连接后运行程序
   - 观察异常处理和错误提示

2. **参数验证**
   - 输入空值或格式错误的参数
   - 验证输入验证逻辑

## 🔧 配置说明

### 商户信息
```
商户号: NJSJ1738988721355510
密钥: ygAlUjvUnvq2KrYCuxzT5616C6lBksxk
```

### 接口地址
```
用户有效订单查询: http://buss.haochengda.net:8088/gzs/allRunningOrderQuery
IMEI编码查询: http://buss.haochengda.net:8088/gzs/queryValidImei
```

## 💡 使用建议

1. **首次使用**: 建议先运行基础版本，验证网络连接和基本功能
2. **参数测试**: 使用手动输入版本测试不同的参数组合
3. **错误处理**: 故意输入错误参数，测试系统的错误处理能力
4. **业务验证**: 验证公证处的业务规则（如5台设备限制）

## 📞 技术支持

如果在使用过程中遇到问题：
1. 检查网络连接是否正常
2. 验证输入参数格式是否正确
3. 查看控制台输出的详细错误信息
4. 确认商户号和密钥配置是否正确

## 🎉 功能特点

✅ **完整实现**: 两个接口功能完全实现
✅ **参数验证**: 完整的输入格式验证
✅ **错误处理**: 全面的异常处理机制
✅ **用户友好**: 清晰的提示和交互界面
✅ **业务规则**: 实现公证处的业务规则验证
✅ **灵活使用**: 支持固定参数和手动输入两种模式

现在您可以根据实际需求选择合适的功能进行测试和使用！
